/*
 * Copyright (C) 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
#pragma once

#include "ClearKeyTypes.h"

namespace clearkeydrm {

class InitDataParser {
  public:
    InitDataParser() {}

    CdmResponseType parse(const std::vector<uint8_t>& initData, const std::string& mimeType,
                          CdmKeyType keyType, std::vector<uint8_t>* licenseRequest);

  private:
    CLEARKEY_DISALLOW_COPY_AND_ASSIGN(InitDataParser);

    CdmResponseType parsePssh(const std::vector<uint8_t>& initData,
                              std::vector<const uint8_t*>* keyIds);

    std::string generateRequest(CdmKeyType keyType, const std::vector<const uint8_t*>& keyIds);
};

}  // namespace clearkeydrm
