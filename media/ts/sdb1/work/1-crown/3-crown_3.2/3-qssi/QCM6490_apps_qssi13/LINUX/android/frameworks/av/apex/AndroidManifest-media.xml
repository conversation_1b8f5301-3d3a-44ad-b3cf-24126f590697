<?xml version="1.0" encoding="utf-8"?>
<!--
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 -->
<manifest xmlns:android="http://schemas.android.com/apk/res/android"
  package="com.android.media">
  <!-- APEX does not have classes.dex -->
  <application android:hasCode="false" />
  <!-- Setting maxSdk to lock the module to Q. minSdk is auto-set by build system -->
  <!-- TODO: Uncomment this when the R API level is fixed. b/148281152 -->
  <!--uses-sdk
      android:maxSdkVersion="29"
      android:targetSdkVersion="29"
  />
  -->
</manifest>
