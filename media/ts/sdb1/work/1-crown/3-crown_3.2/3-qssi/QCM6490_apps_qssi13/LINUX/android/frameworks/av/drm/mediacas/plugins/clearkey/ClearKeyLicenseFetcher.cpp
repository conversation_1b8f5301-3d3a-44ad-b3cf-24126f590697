/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

//#define LOG_NDEBUG 0
#define LOG_TAG "ClearKeyLicenseFetcher"

#include "ClearKeyLicenseFetcher.h"
#include "protos/license_protos.pb.h"

#include <utils/Log.h>
#include <utils/String8.h>
#include "JsonAssetLoader.h"

namespace android {
namespace clearkeycas {

status_t ClearKeyLicenseFetcher::Init(const char *input) {
    JsonAssetLoader *extractor = new JsonAssetLoader();
    return extractor->extractAssetFromString(String8(input), &asset_);
}

status_t ClearKeyLicenseFetcher::FetchLicense(
        uint64_t /* asset_id */, Asset* asset) {
    *asset = asset_;
    return OK;
}

}  // namespace clearkeycas
}  // namespace android
