/******************************************************************************
 *
 * Copyright (C) 2020 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at:
 *
 * http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 *
 *****************************************************************************
 * Originally developed and contributed by Ittiam Systems Pvt. Ltd, Bangalore
 */

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_fuzz {
    name: "mediadrm_fuzzer",
    srcs: [
        "mediadrm_fuzzer.cpp",
    ],
    static_libs: [
        "libmediadrm",
        "liblog",
        "resourcemanager_aidl_interface-ndk",
        "libaidlcommonsupport",
    ],
    header_libs: [
        "libmedia_headers",
        "libmediadrm_headers",
    ],
    shared_libs: [
        "libbinder",
        "libutils",
        "libbinder_ndk",
        "libcutils",
        "libdl",
        "libmedia",
        "libmediadrmmetrics_lite",
        "libmediametrics#1",
        "libmediautils",
        "libstagefright_foundation",
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "android.hardware.drm@1.3",
        "android.hardware.drm@1.4",
        "libhidlallocatorutils",
        "libhidlbase",
        "android.hardware.drm-V1-ndk",
    ],
    fuzz_config: {
        cc: [
            "<EMAIL>",
        ],
        componentid: 155276,
    },
}
