--------- beginning of kernel
01-02 03:44:26.801   580   580 I logd    : logdr: UID=2000 GID=2000 PID=4776 b tail=0 logMask=99 pid=0 start=0ns deadline=0ns
--------- beginning of main
08-27 08:31:27.078   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:31:27.096   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:31:27.096   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:27.096   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
--------- beginning of system
08-27 08:31:27.507  1057  1347 D CompatibilityChangeReporter: Compat change id reported: 173031413; UID 1000; state: ENABLED
08-27 08:31:27.509  1057  1347 D CompatibilityChangeReporter: Compat change id reported: 173031413; UID 1000; state: DISABLED
08-27 08:31:27.509  1057  2010 I PowerGroup: Waking up power group from Dozing (groupId=0, uid=1000, reason=WAKE_REASON_POWER_BUTTON, details=android.policy:POWER)...
08-27 08:31:27.511  1057  2010 I PowerManagerService: Waking up from Dozing (uid=1000, reason=WAKE_REASON_POWER_BUTTON, details=android.policy:POWER)...
08-27 08:31:27.515  1057  1057 E ActivityManager: Cancel pending or running compactions as system is awake
08-27 08:31:27.519  1057  1057 D OomAdjuster: Not killing cached processes
08-27 08:31:27.525  1057  1419 I DisplayPowerController[0]: Blocking screen on until initial contents have been drawn.
08-27 08:31:27.539  1057  1419 V DisplayPowerController[0]: Brightness [0.48818898] reason changing to: 'manual', previous reason: 'screen_off'.
08-27 08:31:27.539  1057  1419 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.0, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 08:31:27.543  1057  1419 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.48818898, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 08:31:27.548  1018  1018 I sensors-hal: batch:240, android.sensor.accelerometer/11, period=200000000, max_latency=2000000000
08-27 08:31:27.548  1018  1018 I sensors-hal: set_config:64, sample_period_ns is adjusted to 200000000 based on min/max delay_ns
08-27 08:31:27.548  1018  1018 I sensors-hal: batch:249, android.sensor.accelerometer/11, period=200000000, max_latency=2000000000 request completed
08-27 08:31:27.549  1018  1018 I sensors-hal: activate:207, android.sensor.accelerometer/11 en=1
08-27 08:31:27.550  1018  1018 I sensors-hal: get_qmi_debug_flag:241, support_qmi_debug : false
08-27 08:31:27.551   996   996 I QTI PowerHAL: Power setMode: 7 to: 1
08-27 08:31:27.552   996   996 I QTI PowerHAL: Got set_interactive hint
01-02 03:44:27.402     0     0 I [    C0] ALERT: changing window size from 16000000 to 20000000 at 130580096146
08-27 08:31:27.553   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fa went down
08-27 08:31:27.555  1057  1385 E system_server: Cannot read thread CPU times for PID 1057
08-27 08:31:27.557   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fb went down
08-27 08:31:27.558  1138  1138 D SurfaceFlinger: Setting power mode 2 on display 4630946754821040514
08-27 08:31:27.558   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fc went down
08-27 08:31:27.558  1033  1033 I SDM     : DisplayBase::SetDisplayState: Set state = 1, display 54-0, teardown = 0
08-27 08:31:27.560  1057  1349 I DisplayDeviceRepository: Display device changed state: "内置屏幕", ON
01-02 03:44:27.410   386   386 I [drm:dsi_display_set_mode [msm_drm]] [msm-dsi-info]: mdp_transfer_time=0, hactive=240, vactive=320, fps=60
01-02 03:44:27.411   386   386 I [drm:dsi_ctrl_isr_configure [msm_drm]] [msm-dsi-info]: dsi-ctrl-0: IRQ 317 registered
08-27 08:31:27.564  1018  1018 I sensors-hal: send_sync_sensor_request:358, send sync request
08-27 08:31:27.564  1018  1018 I sensors-hal: send_sync_sensor_request:384, wait for notification of response
08-27 08:31:27.564   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fd went down
08-27 08:31:27.564  1057  1349 I InputManager-JNI: Viewport [0] to add: local:4630946754821040514, isActive: true
08-27 08:31:27.567  1057  2010 I InputReader: Reconfiguring input devices, changes=DISPLAY_INFO | 
08-27 08:31:27.567  1057  2010 I InputReader: Device reconfigured: id=4, name='chsc_cap_touch', size 320x240, orientation 0, mode 1, display id 0
08-27 08:31:27.572  1018  4781 I sensors-hal: ssc_conn_resp_cb:663, resp_value = 0
08-27 08:31:27.572  1018  1018 I sensors-hal: send_sync_sensor_request:390, takes 8 ms to receive the response with 0
08-27 08:31:27.573  1018  1018 I sensors-hal: activate:218, android.sensor.accelerometer/11 en=1 completed
08-27 08:31:27.577  3596  3596 E SUI_svcsock: svc_sock_send_message(suisvc): invalid remote socket suilst
08-27 08:31:27.578  2284  2791 I BtGatt.ScanManager: msg.what = 7
08-27 08:31:27.578  2284  2791 D BtGatt.ScanManager: handleScreenOn()
08-27 08:31:27.579  3430  3430 D NfcService: MSG_APPLY_SCREEN_STATE 8
08-27 08:31:27.582  3430  3430 D NfcService: applyRouting enter
08-27 08:31:27.583  1057  1350 I DisplayDevice: [0] Layerstack set to 0 for local:4630946754821040514
08-27 08:31:27.584  3430  3430 D NfcService: Discovery configuration equal, not updating.
08-27 08:31:27.584  3430  3430 I libnfc_nci: [INFO:NativeNfcManager.cpp(2992)] nfcManager_doSetScreenState: state = 8 prevScreenState= 1, discovry_param = 1
08-27 08:31:27.584  3480  3480 D SYS_EVENT: send event 64 notification to client
08-27 08:31:27.586  3430  3430 I libnfc_nci: [INFO:nfa_dm_api.cc(236)] NFA_SetPowerSubStateForScreenState: state:0x8
08-27 08:31:27.586  3430  3430 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007032f92080 39:41
08-27 08:31:27.586  3430  3868 I libnfc_nci: [INFO:nfa_sys_main.cc(96)] NFA got event 0x011A
08-27 08:31:27.587  3430  3868 I libnfc_nci: [INFO:nfa_dm_main.cc(153)] event: NFA_DM_API_SET_POWER_SUB_STATE_EVT (0x1a)
08-27 08:31:27.587  3430  3868 I libnfc_nci: [INFO:nfa_dm_act.cc(674)] nfa_dm_set_power_sub_state
08-27 08:31:27.587  3430  3868 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007102f4ee40 40:41
08-27 08:31:27.587  3430  3868 I libnfc_nci: [INFO:nfc_ncif.cc(497)] nfc_ncif_send_cmd()
08-27 08:31:27.588  3430  3868 I libnfc_nci: [INFO:nfc_ncif.cc(145)] check_and_store_last_cmd:
08-27 08:31:27.588  3430  3868 I libnfc_nci: [INFO:NfcAdaptation.cc(931)] NfcAdaptation::HalWrite
08-27 08:31:27.589  1066  3887 D NxpTml  : PN54X - Write requested.....
08-27 08:31:27.589  1066  3887 D NxpTml  : PN54X - Invoking I2C Write.....
01-02 03:44:27.439  3887  3887 E i2c_geni a84000.i2c: i2c error :-107
01-02 03:44:27.445  3887  3887 W i2c_write: write failed ret(-107), maybe in standby
08-27 08:31:27.597  1066  3887 D NxpTml  : Write errno : 6b
08-27 08:31:27.597  1066  3887 D NxpTml  : PN54X - Error in I2C Write.....
08-27 08:31:27.598  1066  3887 D NxpTml  : PN54X - Posting Fresh Write message.....
08-27 08:31:27.598  1066  3890 D NxpHal  : write error status = 0x1ff
08-27 08:31:27.598  1066  1066 D NxpHal  : write_unlocked failed - PN54X Maybe in Standby Mode - Retry
08-27 08:31:27.599  1066  3887 D NxpTml  : PN54X - Tml Writer Thread Running................
08-27 08:31:27.604   905  3532 D audio_hw_primary: adev_set_parameters: enter: screen_state=on
08-27 08:31:27.606   905  3532 D sound_trigger_hw: handle_screen_status_change: screen on
08-27 08:31:27.608  1066  3887 D NxpTml  : PN54X - Write requested.....
08-27 08:31:27.608  1066  3887 D NxpTml  : PN54X - Invoking I2C Write.....
08-27 08:31:27.609  1066  3887 D NxpNciX : len =   4 > 20090100
08-27 08:31:27.609  1066  3887 D NxpTml  : PN54X - I2C Write successful.....
08-27 08:31:27.609  1066  3887 D NxpTml  : PN54X - Posting Fresh Write message.....
08-27 08:31:27.609  1066  3887 D NxpTml  : PN54X - Tml Writer Thread Running................
08-27 08:31:27.609  1066  3890 D NxpHal  : write successful status = 0x0
08-27 08:31:27.610  1057  1347 I Pointer : Device Changed: Input Device 4: chsc_cap_touch
08-27 08:31:27.610  1057  1347 I Pointer :   Descriptor: 524d36e220110f6ef21ee0aa368c054e5d318528
08-27 08:31:27.610  1057  1347 I Pointer :   Generation: 18
08-27 08:31:27.610  1057  1347 I Pointer :   Location: built-in
08-27 08:31:27.610  1057  1347 I Pointer :   Keyboard Type: none
08-27 08:31:27.610  1057  1347 I Pointer :   Has Vibrator: false
08-27 08:31:27.610  1057  1347 I Pointer :   Has Sensor: false
08-27 08:31:27.610  1057  1347 I Pointer :   Has battery: false
08-27 08:31:27.610  1057  1347 I Pointer :   Has mic: false
08-27 08:31:27.610  1057  1347 I Pointer :   Sources: 0x1002 ( touchscreen )
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_X: source=0x1002 min=0.0 max=319.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_Y: source=0x1002 min=0.0 max=239.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_PRESSURE: source=0x1002 min=0.0 max=1.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_SIZE: source=0x1002 min=0.0 max=1.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_TOUCH_MAJOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_TOUCH_MINOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_TOOL_MAJOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1057  1347 I Pointer :     AXIS_TOOL_MINOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 08:31:27.610  1066  3886 D NxpTml  : PN54X - I2C Read successful.....
08-27 08:31:27.610  1066  3886 D NxpNciR : len =   4 > 40090100
08-27 08:31:27.610  1066  3886 D NxpTml  : PN54X - Posting read message.....
08-27 08:31:27.611  1066  3890 D NxpHal  : read successful status = 0x0
08-27 08:31:27.612  3430  3827 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007102f4e800 39:41
08-27 08:31:27.613  1066  3886 D NxpTml  : PN54X - Read requested.....
08-27 08:31:27.613  1066  3886 D NxpTml  : PN54X - Invoking I2C Read.....
08-27 08:31:27.614  3430  3868 I libnfc_nci: [INFO:nfc_ncif.cc(583)] NFC received rsp gid:0
08-27 08:31:27.615  3430  3868 I libnfc_nci: [INFO:nci_hrcv.cc(87)] nci_proc_core_rsp opcode:0x9
08-27 08:31:27.616  3430  3868 I libnfc_nci: [INFO:nfa_dm_act.cc(295)] unknown revt(0x5012)
08-27 08:31:27.617  3430  3868 I libnfc_nci: [INFO:NativeNfcManager.cpp(1027)] nfaDeviceManagementCallback: enter; event=0xB
08-27 08:31:27.618  3430  3868 I libnfc_nci: [INFO:NativeNfcManager.cpp(1231)] nfaDeviceManagementCallback: NFA_DM_SET_POWER_SUB_STATE_EVT; status=0x0
08-27 08:31:27.619  3430  3430 I libnfc_nci: [INFO:nfa_dm_api.cc(294)] param_id:0x2
08-27 08:31:27.620  3430  3430 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007042f9bbe0 39:41
08-27 08:31:27.620  1057  1057 D FastPairService: onReceive: ACTION_SCREEN_ON or boot complete.
08-27 08:31:27.620  1057  1057 V FastPairService: invalidateScan: scan is disabled
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:nfa_sys_main.cc(96)] NFA got event 0x0102
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:nfa_dm_main.cc(153)] event: NFA_DM_API_SET_CONFIG_EVT (0x02)
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:nfa_dm_main.cc(257)] nfa_dm_check_set_config
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007102f4e800 40:41
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:nfc_ncif.cc(497)] nfc_ncif_send_cmd()
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:nfc_ncif.cc(145)] check_and_store_last_cmd:
08-27 08:31:27.621  3430  3868 I libnfc_nci: [INFO:NfcAdaptation.cc(931)] NfcAdaptation::HalWrite
08-27 08:31:27.621  1057  1057 E NearbyManager: Cannot stop scan with this callback because it is never registered.
08-27 08:31:27.622  1066  3887 D NxpTml  : PN54X - Write requested.....
08-27 08:31:27.622  1066  3887 D NxpTml  : PN54X - Invoking I2C Write.....
08-27 08:31:27.622  1066  3887 D NxpNciX : len =   7 > 20020401020101
08-27 08:31:27.622  1066  3887 D NxpTml  : PN54X - I2C Write successful.....
08-27 08:31:27.622  1066  3887 D NxpTml  : PN54X - Posting Fresh Write message.....
08-27 08:31:27.622  1066  3887 D NxpTml  : PN54X - Tml Writer Thread Running................
08-27 08:31:27.622  1066  3890 D NxpHal  : write successful status = 0x0
08-27 08:31:27.623  1066  3886 D NxpTml  : PN54X - I2C Read successful.....
08-27 08:31:27.623  1066  3886 D NxpNciR : len =   5 > 4002020000
08-27 08:31:27.623  1066  3886 D NxpTml  : PN54X - Posting read message.....
08-27 08:31:27.623  1066  3890 D NxpHal  : read successful status = 0x0
08-27 08:31:27.623  1066  3890 D NxpHal  : phNxpNciHal_print_res_status: response status =STATUS_OK
08-27 08:31:27.623  3430  3827 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007102f4a660 41:41
08-27 08:31:27.624  1066  3886 D NxpTml  : PN54X - Read requested.....
08-27 08:31:27.624  1066  3886 D NxpTml  : PN54X - Invoking I2C Read.....
08-27 08:31:27.625  3430  3868 I libnfc_nci: [INFO:nfc_ncif.cc(583)] NFC received rsp gid:0
08-27 08:31:27.625  3430  3868 I libnfc_nci: [INFO:nci_hrcv.cc(87)] nci_proc_core_rsp opcode:0x2
08-27 08:31:27.625  3430  3868 I libnfc_nci: [INFO:nfa_dm_act.cc(295)] NFC_SET_CONFIG_REVT(0x5002)
08-27 08:31:27.625  3430  3868 I libnfc_nci: [INFO:NativeNfcManager.cpp(1027)] nfaDeviceManagementCallback: enter; event=0x2
08-27 08:31:27.625  3430  3868 I libnfc_nci: [INFO:NativeNfcManager.cpp(1056)] nfaDeviceManagementCallback: NFA_DM_SET_CONFIG_EVT
08-27 08:31:27.627  3511  3807 I EventProvider: [EventProviderApp] handleIntent: action = android.intent.action.SCREEN_ON
08-27 08:31:27.627  3511  3807 D EventProvider: [EventProviderApp] LedEventListener: onEventReceived begin
08-27 08:31:27.627  3511  3807 W EventProvider: [EventProviderApp] LedEventListener: onEventReceived value = 0
08-27 08:31:27.627  3511  3807 I TcLightsManager: handleEvent: eventType = 11
08-27 08:31:27.629  3511  3807 I EventProvider: [EventProviderApp] LedEventListener: onEventReceived finished !
08-27 08:31:27.629  3511  3807 I EventProvider: [EventProviderApp] response wakeup
08-27 08:31:27.632  1057  2311 E ActivityManager: Sending non-protected broadcast yellowstone.system.SYS_WAKEUP from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:31:27.632  1057  2311 E ActivityManager: java.lang.Throwable
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:31:27.632  1057  2311 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:31:27.634  2543  2669 D McuOtaServiceApp_EventBrokerManager: onEventReceived: Event received - power_save_state
08-27 08:31:27.634  2543  2669 I McuOtaServiceApp_EventBrokerManager: onEventReceived: categoryId: 2, typeId: 10
08-27 08:31:27.634  3511  3807 D EventProvider: [EventProviderApp] EventBrokerManager: JSON event published successfully - power_save_state
08-27 08:31:27.634  3511  3807 I EventProvider: [EventProviderApp] writeGpioActive: start
08-27 08:31:27.634  3511  3807 I EventProvider: [EventProviderApp] writeGpioFile: filePath=/sys/devices/platform/soc/soc:gpio_info/qcm_info_mcu2_gpio79, target=1
08-27 08:31:27.635  2543  3826 I McuOtaServiceApp_EventBrokerManager: handleEventBrokerEvent: POWER_SAVE event received
08-27 08:31:27.636  2543  3826 I McuOtaServiceApp_EventBrokerManager: handleEventBrokerEvent: POWER_SAVE exit
08-27 08:31:27.636  2200  2200 D CentralSurfaces: mShouldDelayWakeUpAnimation CLEARED
08-27 08:31:27.637  2200  2200 W OnBackInvokedCallback: OnBackInvokedCallback is not enabled for the application.
08-27 08:31:27.637  2200  2200 W OnBackInvokedCallback: Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
08-27 08:31:27.637  2200  2200 D SbStateController: setState: requested state SHADE!= upcomingState: KEYGUARD. This usually means the status bar state transition was interrupted before the upcoming state could be applied.
08-27 08:31:27.642  3837  3837 D qfp-service: screen on
08-27 08:31:27.643  1856  1856 I QFP     : notifyPowerState: entry
08-27 08:31:27.643  1856  1856 I QFP     : getPersistPropVal: persist.vendor.qfp = true (default false)
08-27 08:31:27.643  1856  1856 I QFP     : getPersistPropVal: persist.vendor.qfp.wup_display = 0 (default 0)
08-27 08:31:27.643  1856  1856 I QFP     : notifyPowerState: wake isEnableFD=true
08-27 08:31:27.643  1856  1856 I QFP     : Initializing QFP framework, container 0 handle 0
08-27 08:31:27.643  1856  1856 I QFP     : getPersistPropVal: persist.vendor.qfp.qbt_fd_name = /dev/qbt_fd (default /dev/qbt_fd)
08-27 08:31:27.643  1856  1856 E QFP     : openSensor: failed to open /dev/qbt_fd
08-27 08:31:27.643  1856  1856 E QFP     : qfp_init failed -1
08-27 08:31:27.644  2200  2200 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 08:31:27.645  1057  2094 D ActivityTaskManager: Top Process State changed to PROCESS_STATE_TOP
08-27 08:31:27.650  3511  3807 I EventProvider: [EventProviderApp] writeGpioActive: result79=true
08-27 08:31:27.652  2200  2200 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 08:31:27.654  1057  1057 I SideFpsEventHandler: notifyPowerPressed
08-27 08:31:27.655  1057  1057 I FingerprintManager: onPowerPressed
08-27 08:31:27.656  1057  1057 I WindowManager: Suppressed redundant power key press while already in the process of turning the screen on.
08-27 08:31:27.656  2200  2200 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 08:31:27.658  1057  1057 E Fingerprint21: onPowerPressed not supported for HIDL clients
08-27 08:31:27.661  2200  2200 D CentralSurfaces: Received new disable state: enaihbcrso.qingr (unchanged)
08-27 08:31:27.665  1057  2311 I ActivityManager: updateOomAdj start time is before than pendingPid added, don't delete it
08-27 08:31:27.667  2200  2200 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 08:31:27.670  2365  2365 D DeviceStatisticsService: Send device is interactive
08-27 08:31:27.676  3596  3596 E SUI_svcsock: svc_sock_send_message(suisvc): invalid remote socket suilst
08-27 08:31:27.678  3480  3480 D SYS_EVENT: send event 16 notification to client
08-27 08:31:27.681  2819  3513 E OpenGLRenderer: Unable to match the desired swap behavior.
08-27 08:31:27.686  3476  3476 D PASRDozeReceiver: Received Intent Intent { act=android.intent.action.SCREEN_ON flg=0x50200010 }
08-27 08:31:27.693  3476  3476 D PASRDozeReceiver: Sent intent Successfully
08-27 08:31:27.694  3476  3476 D PASRService: PASRService started
08-27 08:31:27.695  3476  3476 D PASRService: OnStartteett, Intent: android.intent.action.SCREEN_ON
08-27 08:31:27.695  3476  3476 D PASRService: Screen Off timer prop: 0. val: 0
08-27 08:31:27.699  3476  3476 I android_os_HwBinder: HwBinder: Starting thread pool for getting: vendor.qti.memory.pasrmanager@1.0::IPasrManager/pasrhal
08-27 08:31:27.700  3476  3476 D PASRService: is ActiveMode Enabled: false
08-27 08:31:27.700  3476  3476 D PASRService: Attempting Online All
08-27 08:31:27.703  3476  3476 D PASRService: State is now: -10
08-27 08:31:27.728  1057  1419 I DisplayPowerController[0]: Unblocked screen on after 203 ms
08-27 08:31:27.729  1018  1018 I sensors-hal: batch:240, android.sensor.device_orientation/272, period=66667000, max_latency=0
08-27 08:31:27.729  1018  1018 I sensors-hal: set_config:69, no need to adjust sample_period_ns due to on_change_sp_enabled = 1 or reporting_mode = 2
08-27 08:31:27.730  1018  1018 I sensors-hal: batch:249, android.sensor.device_orientation/272, period=66667000, max_latency=0 request completed
08-27 08:31:27.730  1018  1018 I sensors-hal: activate:207, android.sensor.device_orientation/272 en=1
08-27 08:31:27.731  1018  1018 I sensors-hal: get_qmi_debug_flag:241, support_qmi_debug : false
08-27 08:31:27.733   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41ff went down
08-27 08:31:27.737  1057  1419 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.48818898, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 08:31:27.738   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4200 went down
08-27 08:31:27.741   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4201 went down
08-27 08:31:27.748  1018  1018 I sensors-hal: send_sensor_config_request:481, dt=device_orient: convert sample_period=66667000 to batch_period=66667
08-27 08:31:27.749  1018  1018 I sensors-hal: send_sync_sensor_request:358, send sync request
08-27 08:31:27.749  1018  1018 I sensors-hal: send_sync_sensor_request:384, wait for notification of response
08-27 08:31:27.750   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4202 went down
08-27 08:31:27.751  1018  4788 I sensors-hal: ssc_conn_resp_cb:663, resp_value = 0
08-27 08:31:27.752  1018  1018 I sensors-hal: send_sync_sensor_request:390, takes 2 ms to receive the response with 0
08-27 08:31:27.752  1018  1018 I sensors-hal: activate:218, android.sensor.device_orientation/272 en=1 completed
08-27 08:31:27.755  1018  4781 I sensors-hal: handle_indication_realtime:471,  SCHED_FIFO(10) for qmi_cbk
08-27 08:31:27.897  1033  1033 I SDM     : DisplayBase::SetDisplayState: active 1-1 state 1-1 pending_power_state_ 0
08-27 08:31:27.899  1138  1138 D DisplayDevice: getVsyncPeriodFromHWC: Called HWC getDisplayVsyncPeriod. No error. period=16666666
08-27 08:31:27.899  1138  1138 D SurfaceFlinger: Finished setting power mode 2 on display 4630946754821040514
08-27 08:31:27.900  1057  2121 D SurfaceControl: Excessive delay in setPowerMode()
08-27 08:31:27.901  1057  2121 E DisplayDeviceConfig: requesting nits when no mapping exists.
08-27 08:31:27.902  1057  1349 I DisplayDeviceRepository: Display device changed state: "内置屏幕", ON
08-27 08:31:27.902  1057  2121 E DisplayDeviceConfig: requesting nits when no mapping exists.
08-27 08:31:27.918  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:27.919  1057  1419 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.48818898, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
01-02 03:44:27.762   386   386 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:27.763   386   386 I dsi_panel_set_backlight: start to update st7789vi lcd backlight, bl_lvl=0, bl_max_level=255, duty=0%
01-02 03:44:27.763   386   386 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:27.763   386   386 F         : [CHSC] function = semi_touch_reset              , line = 14  : set status before reset tp...
01-02 03:44:27.774   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:27.929  1057  1419 I LatencyTracker: ACTION_TURN_ON_SCREEN latency=411
08-27 08:31:27.933  1057  1419 W PowerManagerService: Screen on took 447 ms
08-27 08:31:27.935  2284  2791 I BtGatt.ScanManager: msg.what = 7
08-27 08:31:27.936  1057  1419 I DreamManagerService: Gently waking up from dream.
08-27 08:31:28.001   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:31:28.001   731   731 I mpu_uart: [TIME-:118]:delete
08-27 08:31:28.002   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x30, 0x35, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x2f, ]
08-27 08:31:28.002   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20305,current_state=0, ota_state = 1
08-27 08:31:28.002   731   745 V mpu_uart:  >> log: 
08-27 08:31:28.016   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:31:28.017   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x38, 0x37, 0x20, 0x6d, 0x76, 0xa, 0x7b, ]
08-27 08:31:28.017   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12387 mv
08-27 08:31:28.017   731   745 V mpu_uart:  >> log: 
08-27 08:31:28.021   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:31:28.022   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:31:28.030   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:31:28.030   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:31:28.030   731   747 I mpu_uart: [TIME-:119]:delete
08-27 08:31:28.030   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:31:28.030   731   745 V mpu_uart:  >> log: 
08-27 08:31:28.032  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:31:28.032  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:31:28.032   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:28.033   731   747 I mpu_uart: [TIME-:120]:create
01-02 03:44:27.882   386   386 F         : [CHSC] function = semi_touch_reset_and_detect   , line = 463 : set status pointing...
01-02 03:44:27.893   386   386 F         : [CHSC] function = semi_touch_resume_entry       , line = 625 : tpd_resume...
08-27 08:31:28.050   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:31:28.051   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:31:28.048  1598  1598 I tlog    : type=1400 audit(0.0:176): avc: denied { rename } for name="-00001_persist_00024_250827_083051.log.ing" dev="dm-6" ino=14672 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:31:28.052  1598  1598 I tlog    : type=1400 audit(0.0:177): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
08-27 08:31:28.058   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:28.056   578   578 W hwservicemanage: type=1400 audit(0.0:178): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:31:28.058   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:31:28.058   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
01-02 03:44:27.909   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
08-27 08:31:28.058   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:28.058   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x63, 0xf8, ]
01-02 03:44:27.909   578   578 I binder  : send failed reply for transaction 69271 to 1598:1620
08-27 08:31:28.059   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:28.059   731   747 I mpu_uart: [TIME-:120]:delete
08-27 08:31:28.060  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:31:28.060  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:31:28.060  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:31:28.060  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:31:28.060  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:31:28.060  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:31:28.062  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x63 
08-27 08:31:28.062  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:31:28.062  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:31:28.062  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12387
08-27 08:31:28.062  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12387
08-27 08:31:28.063   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:31:28.063   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 08:31:28.063   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:28.065  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 08:31:28.065  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:31:28.066  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:31:28.066  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 08:31:28.067  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 08:31:28.067  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
01-02 03:44:27.917  1033  1033 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:27.917  1033  1033 I dsi_panel_set_backlight: start to update st7789vi lcd backlight, bl_lvl=124, bl_max_level=255, duty=48%
01-02 03:44:27.917  1033  1033 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:28.070  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:28.072   578   578 W hwservicemanage: type=1400 audit(0.0:179): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:31:28.076  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:31:28.076  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:31:28.076  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:44:27.925   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:44:27.925   578   578 I binder  : send failed reply for transaction 69280 to 1598:1620
08-27 08:31:28.084  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:28.093  2200  2200 D CentralSurfaces: Received new disable state: enaihbcrso.qingr (unchanged)
08-27 08:31:28.088  1598  1598 I tlog    : type=1400 audit(0.0:180): avc: denied { setattr } for name="-00001_persist_00025_250827_083128.log.ing" dev="dm-6" ino=14675 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:31:28.126  1138  1211 W TransactionTracing: Could not find layer handle 0xb40000768a254a10
08-27 08:31:28.126  1138  1211 W TransactionTracing: Could not find layer handle 0xb40000768a254a10
08-27 08:31:28.127  1138  1211 W TransactionTracing: Could not find layer handle 0xb40000768a254a10
08-27 08:31:28.131  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:28.142  1057  3765 I DreamManagerService: Leaving dreamland.
08-27 08:31:28.142  1057  1346 I DreamController: Stopping dream: name=ComponentInfo{com.android.systemui/com.android.systemui.doze.DozeService}, isPreviewMode=false, canDoze=true, userId=0, reason='finished self'(from 'power manager request')
08-27 08:31:28.152  2200  2458 D ControlsListingControllerImpl: Unsubscribing callback
08-27 08:31:28.157  2200  2458 D ControlsListingControllerImpl: Unsubscribing callback
08-27 08:31:28.158  2200  2458 D ControlsListingControllerImpl: Unsubscribing callback
08-27 08:31:28.160  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:28.183  2200  2200 D SbStateController: setIsDreaming:false
08-27 08:31:28.283  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:28.563  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:31:28.565  2543  2743 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:31:28.568  3511  3822 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:31:28.569  3511  4058 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:31:28.569  3511  4058 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:31:28.569  3511  4058 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@bd694b8
08-27 08:31:28.569  3511  4058 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:31:28.570  3511  4058 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:31:28.571  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:31:28.571  2543  2743 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:31:28.572  2543  2743 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:31:28.572  2543  2743 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:31:28.572  1057  2094 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:31:28.572  1057  2094 E ActivityManager: java.lang.Throwable
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:31:28.572  1057  2094 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:31:28.572   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:31:28.573   731   747 I mpu_uart: [TIME-:121]:create
08-27 08:31:28.573   731   744 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:31:28.574   731   744 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:31:28.577   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:28.577   731   747 I mpu_uart: [TIME-:121]:delete
08-27 08:31:28.577   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:31:28.578   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:31:28.578  2543  2743 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:31:28.578   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:28.579   731   747 I mpu_uart: [TIME-:122]:create
08-27 08:31:28.586  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:31:28.587  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:31:28.587  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:31:28.598   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:28.598   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:31:29.063  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:31:29.063  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:31:29.063  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:31:29.065   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:29.065   731   731 I mpu_uart: [TIME-:123]:create
08-27 08:31:29.578   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:31:29.599   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:31:29.599   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:29.599   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:44:30.381   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.389   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.398   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.407   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.416   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.425   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.579   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-02 03:44:30.434   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.443   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.600   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:31:30.600   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:30.600   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:44:30.452   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.608  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.461   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.470   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.625  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.479   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.488   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.497   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.506   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.515   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.670  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.524   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.533   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.542   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.551   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.705  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.560   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.569   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.722  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.578   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.738  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.587   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.596   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.756  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.605   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.614   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.772  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.623   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.632   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.789  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.641   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.651   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.806  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.660   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.669   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.823  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.678   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.687   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.840  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.696   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.705   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.858  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.714   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.723   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.732   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.891  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.741   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.750   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.908  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.759   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.768   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.925  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.777   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.786   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.942  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.795   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.804   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.959  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.813   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.822   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:30.978  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.831   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.840   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.849   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.858   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.012  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.867   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.876   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.885   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.045  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.894   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.903   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.062  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.912   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.921   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.078  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.930   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.939   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.096  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.949   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.958   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.112  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.967   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.976   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.130  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:30.985   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:30.994   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.147  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.003   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.164  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.012   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.021   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.030   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.039   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.198  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.048   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.057   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.215  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.066   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.075   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.231  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.084   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.093   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.248  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.102   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.111   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.265  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.120   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.129   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.283  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.138   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.300  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.147   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.156   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.165   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.316  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.174   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.331  2507  3940 D MonitorTool_MonitorAppService: [08-27 08:31:31.331] [pool-3-thread-1:36] Starting to check status of 3 apps
08-27 08:31:31.334  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.183   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.342  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:31:31.340] [pool-3-thread-1:36] App[drvsys] not detected running
08-27 08:31:31.343  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:31:31.342] [pool-3-thread-1:36] App[drvsys] running status: Not running
08-27 08:31:31.344  2507  3940 W MonitorTool_MonitorAppService: [08-27 08:31:31.343] [pool-3-thread-1:36] App[drvsys] not running, attempting to start
01-02 03:44:31.192   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.346  2507  3940 E MonitorTool_MonitorAppService: [08-27 08:31:31.346] [pool-3-thread-1:36] Could not get launch intent for app[drvsys], it might not be a launchable app
08-27 08:31:31.350  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:31.351  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:31:31.350] [pool-3-thread-1:36] App[com.thundercomm.testapp] not detected running
08-27 08:31:31.351  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:31:31.351] [pool-3-thread-1:36] App[com.thundercomm.testapp] running status: Not running
08-27 08:31:31.351  2507  3940 W MonitorTool_MonitorAppService: [08-27 08:31:31.351] [pool-3-thread-1:36] App[com.thundercomm.testapp] not running, attempting to start
01-02 03:44:31.202   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.354  2507  3940 E MonitorTool_MonitorAppService: [08-27 08:31:31.353] [pool-3-thread-1:36] Could not get launch intent for app[com.thundercomm.testapp], it might not be a launchable app
08-27 08:31:31.356  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:31:31.355] [pool-3-thread-1:36] App[com.ssol.titanApp] not detected running
08-27 08:31:31.357  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:31:31.356] [pool-3-thread-1:36] App[com.ssol.titanApp] running status: Not running
08-27 08:31:31.357  2507  3940 W MonitorTool_MonitorAppService: [08-27 08:31:31.357] [pool-3-thread-1:36] App[com.ssol.titanApp] not running, attempting to start
08-27 08:31:31.359  2507  3940 E MonitorTool_MonitorAppService: [08-27 08:31:31.358] [pool-3-thread-1:36] Could not get launch intent for app[com.ssol.titanApp], it might not be a launchable app
01-02 03:44:31.211   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.367  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.220   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.229   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.381  1053  1053 D vendor.qti.vibrator: Vibrator on for timeoutMs: 20
08-27 08:31:31.382  1053  4796 D vendor.qti.vibrator: Starting on on another thread
08-27 08:31:31.384  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.231    70    70 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
01-02 03:44:31.238   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.241    70    70 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
01-02 03:44:31.247   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.402  1053  4796 D vendor.qti.vibrator: Notifying on complete
01-02 03:44:31.250    70    70 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
01-02 03:44:31.256   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.402  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:31.409  1053  1053 D vendor.qti.vibrator: QTI Vibrator off
08-27 08:31:31.414   905  4692 D audio_hw_primary: start_output_stream: enter: stream(0xee0c64c0)usecase(1: low-latency-playback) devices(0x2) is_haptic_usecase(0)
08-27 08:31:31.415   905  4692 D audio_hw_primary: select_devices for use case (low-latency-playback)
08-27 08:31:31.415   905  4692 I msm8974_platform: platform_check_and_set_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-27 08:31:31.415   905  4692 I msm8974_platform: platform_check_and_set_codec_backend_cfg: new_snd_devices[0] is 2
08-27 08:31:31.415   905  4692 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
01-02 03:44:31.273   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.275  4692  4692 I [Awinic][1-0034]aw882xx_startup: playback enter
01-02 03:44:31.275  4692  4692 E aw_cali_get_read_cali_re: channel:0 open /mnt/vendor/persist/factory/audio/aw_cali.bin failed!
01-02 03:44:31.282   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.418   905  4692 D msm8974_platform: platform_check_codec_backend_cfg:becf: updated afe: bitwidth 16, samplerate 48000 channels 2,backend_idx 0 usecase = 1 device (speaker)
08-27 08:31:31.418   905  4692 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: Codec selected backend: 0 updated bit width: 16 and sample rate: 48000
08-27 08:31:31.418   905  4692 D audio_hw_primary: check_usecases_codec_backend:becf: force routing 0
08-27 08:31:31.418   905  4692 D audio_hw_primary: check_usecases_codec_backend:becf: (93) check_usecases curr device: speaker, usecase device: backends match 0
08-27 08:31:31.418   905  4692 D audio_hw_primary: check_usecases_codec_backend:becf: check_usecases num.of Usecases to switch 0
08-27 08:31:31.418   905  4692 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 08:31:31.418   905  4692 D audio_hw_primary: enable_snd_device: snd_device(2: speaker)
08-27 08:31:31.418   905  4692 D msm8974_platform: platform_get_island_cfg_on_device:island cfg status on snd_device = (speaker 0)
08-27 08:31:31.418   905  4692 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 1, with Raise=0
08-27 08:31:31.419   905  4692 D audio_route: Apply path: speaker
08-27 08:31:31.419   905  4692 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 3, with Raise=0
08-27 08:31:31.419   905  4692 D audio_hw_utils: audio_extn_utils_send_app_type_cfg: usecase->out_snd_device speaker
08-27 08:31:31.419   905  4692 I audio_hw_utils: send_app_type_cfg_for_device PLAYBACK app_type 69937, acdb_dev_id 15, sample_rate 48000, snd_device_be_idx 39
08-27 08:31:31.420   905  4692 D ACDB-LOADER: ACDB -> send_audio_cal, acdb_id = 15, path = 0, app id = 69937, sample rate = 48000, use_case = 0,buffer_idx_w_path =0, afe_sample_rate = 48000, cal_mode = 1, offset_index = 0
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> send_asm_topology
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TOPOLOGY_ID
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> send_adm_topology
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TOPOLOGY_ID
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> send_audtable
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE_SIZE
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> AUDIO_SET_AUDPROC_CAL cal_type[11] acdb_id[15] app_type[69937]
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> send_audvoltable
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE_SIZE
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE, vol index 0
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> AUDIO_SET_VOL_CAL cal type = 12
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE_SIZE
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> send_audstrmtable
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> audstrm_cal->cal_type.cal_data.cal_size = 20
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> send_afe_topology
08-27 08:31:31.421   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_TOPOLOGY_ID
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> GET_AFE_TOPOLOGY_ID for adcd_id 15, Topology Id 112fc
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> send_afe_cal
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE
08-27 08:31:31.422   905  4692 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 08:31:31.422   905  4692 D ACDB-LOADER: Error: ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE Returned = -19
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE
08-27 08:31:31.422   905  4692 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 08:31:31.422   905  4692 D ACDB-LOADER: Error: ACDB AFE returned = -19
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> AUDIO_SET_AFE_CAL cal_type[16] acdb_id[15]
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> send_hw_delay : acdb_id = 15 path = 0
08-27 08:31:31.422   905  4692 D ACDB-LOADER: ACDB -> ACDB_AVSYNC_INFO: ACDB_CMD_GET_DEVICE_PROPERTY
08-27 08:31:31.422   905  4692 D audio_hw_primary: enable_audio_route: apply mixer and update path: low-latency-playback
08-27 08:31:31.422   905  4692 D audio_route: Apply path: low-latency-playback
08-27 08:31:31.424   905  4692 D audio_hw_primary: select_devices: done
08-27 08:31:31.424   905  4692 D msm8974_platform: platform_set_channel_map mixer_ctl_name:Playback Channel Map9
08-27 08:31:31.424   905  4692 D msm8974_platform: platform_set_channel_map: set mapping(1 2 0 0 0 0 0 0) for channel:2
08-27 08:31:31.435  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.286  4692  4692 I [Awinic][1-0034]aw882xx_dev_init_cali_re: read nvram cali failed, use default Re
01-02 03:44:31.291   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.300   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.309   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.318   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.469  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.324  4692  4692 E msm_adsp_init_mixer_ctl_adm_pp_event_queue: failed to get kctl.
01-02 03:44:31.327   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.335  4692  4692 I afe_get_cal_topology_id: port_id = 0x1006 acdb_id = 15 topology_id = 0x112fc cal_type_index=8 ret=0
01-02 03:44:31.336  4692  4692 E send_afe_cal_type: No cal sent for cal_index 0, port_id = 0x1006! ret -22
01-02 03:44:31.336   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.345  4692  4692 I afe_send_hw_delay: port_id 0x1006 rate 48000 delay_usec 474 status 0
01-02 03:44:31.346   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.500   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:31:31.501   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x30, 0x36, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x2c, ]
01-02 03:44:31.349   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.350  4692  4692 I [Awinic][1-0034]aw882xx_mute: mute state=0
01-02 03:44:31.350  4692  4692 I [Awinic][1-0034]aw882xx_spin_set_record_val: do nothing
08-27 08:31:31.501   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20306,current_state=0, ota_state = 1
08-27 08:31:31.501   731   745 V mpu_uart:  >> log: 
08-27 08:31:31.501   731   747 I mpu_uart: [TIME-:122]:delete
01-02 03:44:31.350  4692  4692 I [Awinic][1-0034]aw882xx_spin_set_record_val: set record spin val done
01-02 03:44:31.351   309   309 I [Awinic][1-0034]aw882xx_startup_work: enter
01-02 03:44:31.351   309   309 I [Awinic][1-0034]aw882xx_start_pa: enter
01-02 03:44:31.351   309   309 I [Awinic][1-0034]aw882xx_dev_reg_update: done
01-02 03:44:31.352   309   309 I [Awinic][1-0034]aw_dev_pwd: done
01-02 03:44:31.353  4692  4692 E q6asm_find_cal_by_buf_number: Can't find ASM Cal for cal_index 2 app_type 69937 buffer_number 8
01-02 03:44:31.364   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.516   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:31:31.516   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x34, 0x34, 0x20, 0x6d, 0x76, 0xa, 0x7c, ]
08-27 08:31:31.516   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12344 mv
08-27 08:31:31.516   731   745 V mpu_uart:  >> log: 
01-02 03:44:31.365   309   309 I [Awinic][1-0034]aw_dev_mode1_pll_check: done
01-02 03:44:31.366   309   309 I [Awinic][1-0034]aw_dev_amppd: done
01-02 03:44:31.366   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.519  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:31.521   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:31:31.521   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
01-02 03:44:31.368   309   309 I [Awinic][1-0034]aw_dev_sysst_check: done
01-02 03:44:31.369   309   309 I [Awinic][1-0034]aw_pid_2113_reg_force_set: needn't set reg value
01-02 03:44:31.369   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.370   309   309 I [Awinic][1-0034]aw_dev_uls_hmute: done
08-27 08:31:31.523   905  4692 D audio_hw_primary: start_output_stream: exit
01-02 03:44:31.376   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.529   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:31:31.530   731   731 I mpu_uart: [TIME-:123]:delete
08-27 08:31:31.530   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:31:31.530   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:31:31.530   731   745 V mpu_uart:  >> log: 
08-27 08:31:31.530  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:31:31.531  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:31:31.532   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:31.532   731   731 I mpu_uart: [TIME-:124]:create
01-02 03:44:31.385   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.540   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:31.540   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:31:31.540   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:31.541  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
01-02 03:44:31.389   309   309 I [Awinic][1-0034]aw_dev_mute: done
08-27 08:31:31.542  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:31:31.542  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
01-02 03:44:31.392   309   309 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-02 03:44:31.392   309   309 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-02 03:44:31.392   309   309 I [Awinic][1-0034]aw882xx_monitor_start: enter
01-02 03:44:31.393   309   309 I [Awinic][1-0034]aw882xx_device_start: done
01-02 03:44:31.393   309   309 I [Awinic][1-0034]aw882xx_start_pa: start success
01-02 03:44:31.393   309   309 I [Awinic][1-0034]aw_monitor_get_voltage: chip voltage is 3957
01-02 03:44:31.394   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.394   309   309 I [Awinic][1-0034]aw_monitor_get_temperature: reg val is 0x001c
01-02 03:44:31.394   309   309 I [Awinic][1-0034]aw_monitor_get_temperature: chip temperature = 28
08-27 08:31:31.555   731   744 E mpu_uart: send_buff: 3c0700111491af
01-02 03:44:31.396   309   309 I [Awinic][1-0034]aw_monitor_set_gain: set reg val = 0x15, gain = 0x4
01-02 03:44:31.396   309   309 E [Awinic]aw_check_dsp_ready: rx topo id is 0x0
01-02 03:44:31.403   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.556   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:31:31.556   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x38, 0xa3, ]
08-27 08:31:31.556   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:31.557  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:31.557  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x38 
08-27 08:31:31.557  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:31:31.557  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:31:31.557  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12344
08-27 08:31:31.558  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12344
08-27 08:31:31.558   731   731 I mpu_uart: [TIME-:124]:delete
08-27 08:31:31.561   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:31:31.561   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 08:31:31.561   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:31.563  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 08:31:31.564  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:31:31.564  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:31:31.564  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 08:31:31.565  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
01-02 03:44:31.412   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.565  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 08:31:31.566  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
01-02 03:44:31.421   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.430   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.591  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.439   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.448   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.608  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.457   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.466   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.475   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.484   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.642  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.493   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.502   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.511   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.520   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.676  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.529   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.538   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.547   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.556   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.709  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.565   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.727  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.574   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.583   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.592   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.601   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.759  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.610   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.619   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.776  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.628   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.637   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.793  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.646   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.655   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.810  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.664   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.673   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.827  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.682   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.691   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.844  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.700   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.709   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.861  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.718   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.727   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.878  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.736   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.745   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.754   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.912  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.763   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.773   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.782   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.791   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.947  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.800   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.809   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.818   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.827   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:31.980  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.836   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.845   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.854   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.015  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.863   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.872   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.881   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.890   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.043  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:31:32.044  2543  2743 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:31:32.045  3511  3822 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:31:32.045  3511  3807 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:31:32.045  3511  3807 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:31:32.046  3511  3807 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@9fd9291
08-27 08:31:32.046  3511  3807 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:31:32.046  3511  3807 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:31:32.046  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:31:32.046  2543  2743 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:31:32.046  2543  2743 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:31:32.046  2543  2743 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:31:32.046   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:31:32.046   731   731 I mpu_uart: [TIME-:125]:create
08-27 08:31:32.047  1057  3765 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:31:32.047  1057  3765 E ActivityManager: java.lang.Throwable
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:31:32.047  1057  3765 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:31:32.047  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.899   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.908   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.064  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.917   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.067   731   744 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:31:32.067   731   744 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:31:32.070   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:32.070   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:31:32.070   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:31:32.070   731   731 I mpu_uart: [TIME-:125]:delete
08-27 08:31:32.072  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:31:32.073  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:31:32.074  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:31:32.074  2543  2743 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:31:32.076   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:32.076   731   731 I mpu_uart: [TIME-:126]:create
01-02 03:44:31.926   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.081  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.935   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.090   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:32.090   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:44:31.944   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.098  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.953   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.958   309   309 I (virq   : irq_count)- 3:93798 217:44230 47:19352 57:16331 298:8809 220:7959 300:7285 10:5343 263:4876 313:4362
01-02 03:44:31.958   309   309 I (cpu    : irq_count)- 0:111690 1:40356 2:24296 3:23838 4:6230 5:6799 6:7091 7:7550
01-02 03:44:31.958   309   309 I (ipi    : irq_count)- 0:178011 1:77802 2:0 3:0 4:0 5:57356 6:0
01-02 03:44:31.962   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.115  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.971   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.980   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:31.989   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.148  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:31.998   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.007   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.166  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.016   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.025   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.183  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.034   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.043   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.052   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.062   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.217  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.071   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.080   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.234  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.089   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.098   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.251  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.107   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.267  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.116   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.125   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.284  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.134   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.143   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.301  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.152   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.161   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.318  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.170   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.179   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.334  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.188   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.197   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.352  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.206   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.215   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.369  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.224   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.233   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.387  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.242   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.251   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.260   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.420  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.269   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.278   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.437  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.287   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.296   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.305   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.314   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.323   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.333   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.487  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.342   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.350   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.504  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.360   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.369   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.378   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.387   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.396   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.555  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.405   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.559  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:31:32.560  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:31:32.560  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:31:32.561   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:32.561   731   747 I mpu_uart: [TIME-:127]:create
01-02 03:44:32.414   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.423   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.432   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.589  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.441   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.450   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.459   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.468   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.477   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.486   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.640  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.495   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.504   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.513   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.522   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.531   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.690  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.540   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.549   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.558   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.567   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.576   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.733   905  4692 W audio_hw_primary: out_write: underrun(5) frames_by_time(48205) > out->last_fifo_frames_remaining(384)
01-02 03:44:32.586   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.742  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.595   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.604   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.613   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.622   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.775  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.631   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.640   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.649   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.658   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.809  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.667   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.676   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.685   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.842  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.694   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.703   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.859  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.712   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.721   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.876  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.730   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.893  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.739   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.748   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.757   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.910  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.766   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.928  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.783   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.792   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.944  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.801   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.962  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.810   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.819   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.979  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.828   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.837   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:32.996  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.846   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.855   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.013  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.864   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.873   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.030  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.882   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.046  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.891   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.896     7     7 I sd 0    : 0:0:0: [sda] Synchronizing SCSI cache
01-02 03:44:32.900   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.909   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.063  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.918   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.070   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-02 03:44:32.927   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.080  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.937   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.091   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:31:33.091   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:33.091   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:44:32.946   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.097  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.955   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.115  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.964   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.973   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.131  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:32.982   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:32.991   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.149  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.000   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.009   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.166  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.018   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.027   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.182  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.036   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.045   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.199  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.054   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.063   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.216  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.072   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.081   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.234  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.090   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.250  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.099   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.108   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.267  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.117   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.126   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.284  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.135   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.144   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.301  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.153   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.162   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.319  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.171   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.180   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.335  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.189   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.198   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.352  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.208   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.370  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.216   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.225   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.234   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.243   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.403  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.252   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.261   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.420  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.270   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.279   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.436  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.288   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.297   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.453  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.307   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.316   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.471  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.325   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.334   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.487  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.342   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.504  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.352   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.361   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.522  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.370   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.379   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.388   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.397   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.406   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.556  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.415   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.572  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.424   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.433   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.590  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.442   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.451   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.607  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.460   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.469   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.478   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.487   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.496   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.505   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.658  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.514   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.523   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.532   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.541   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.550   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.709  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.559   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.568   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.577   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.586   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.595   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.604   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.759  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.613   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.775   905  4692 W audio_hw_primary: out_write: underrun(6) frames_by_time(48185) > out->last_fifo_frames_remaining(384)
01-02 03:44:33.622   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.631   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.640   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.649   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.810  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.658   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.667   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.677   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.686   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.695   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.704   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.860  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.713   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.722   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.878  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.731   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.740   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.749   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.758   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.911  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.767   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.776   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.929  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.785   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.794   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.803   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.962  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.812   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.821   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:33.979  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.830   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.839   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.848   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.857   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.013  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.866   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.875   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.030  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.884   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.893   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.047  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.902   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.912   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.071   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-02 03:44:33.921   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.930   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.081  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:34.092   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:31:34.092   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:34.092   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:44:33.939   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.098  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.948   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.957   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.114  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.966   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.975   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.131  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:33.984   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:33.993   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.148  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.002   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.011   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.166  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.020   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.029   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.183  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.038   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.047   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.200  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.056   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.065   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.074   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.234  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.083   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.092   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.251  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.101   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.110   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.119   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.128   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.284  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.137   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.146   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.301  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.155   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.164   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.318  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.173   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.335  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.183   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.192   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.352  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.201   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.210   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.369  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.219   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.228   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.386  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.237   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.246   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.403  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.255   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.264   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.273   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.437  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.290   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.299   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.454  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.308   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.317   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.471  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.326   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.335   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.344   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.353   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.505  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.362   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.522  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.371   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.380   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.539  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.389   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.398   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.555  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.407   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.416   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.572  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.425   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.434   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.590  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.443   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.452   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.607  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.461   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.470   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.479   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.488   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.641  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.497   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.658  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.506   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.515   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.674  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.524   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.533   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.692  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.542   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.551   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.708  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.561   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.569   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.726  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.578   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.588   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.742  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.597   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.606   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.760  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.615   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.624   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.633   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.642   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.651   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.810  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.660   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.817   905  3532 D audio_hw_primary: out_standby: enter: stream (0xee0c64c0) usecase(1: low-latency-playback)
01-02 03:44:34.668  3532  3532 I [Awinic][1-0034]aw882xx_mute: mute state=1
01-02 03:44:34.668  3532  3532 I [Awinic][1-0034]aw882xx_monitor_stop: enter
01-02 03:44:34.669   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.672  3532  3532 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-02 03:44:34.673  3532  3532 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-02 03:44:34.675  3532  3532 I [Awinic][1-0034]aw_dev_uls_hmute: done
01-02 03:44:34.678   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.687   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.696   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.704  3532  3532 I [Awinic][1-0034]aw_dev_mute: done
01-02 03:44:34.705   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.708  3532  3532 I [Awinic][1-0034]aw_dev_amppd: done
01-02 03:44:34.709  3532  3532 I [Awinic][1-0034]aw_dev_pwd: done
01-02 03:44:34.709  3532  3532 I [Awinic][1-0034]aw882xx_device_stop: done
08-27 08:31:34.862  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.714   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.718  3532  3532 E msm_adsp_clean_mixer_ctl_adm_pp_event_queue: failed to get kctl.
01-02 03:44:34.726   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.731  3532  3532 I [Awinic][1-0034]aw882xx_shutdown: stream playback
01-02 03:44:34.732   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.741   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:44:34.749   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.908  1053  1053 D vendor.qti.vibrator: QTI Vibrator off
01-02 03:44:34.758   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.913  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:44:34.767   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:31:34.926   905  3532 D audio_hw_primary: disable_audio_route: reset and update mixer path: low-latency-playback
08-27 08:31:34.928   905  3532 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 2, with Raise=0
08-27 08:31:34.929   905  3532 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 08:31:34.931   905  3532 D audio_hw_primary: disable_snd_device: snd_device(2: speaker)
08-27 08:31:34.931   905  3532 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 0, with Raise=0
08-27 08:31:34.931  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:34.964  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:34.981  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:34.998  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.000   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:31:35.001   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x30, 0x37, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x2d, ]
08-27 08:31:35.001   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20307,current_state=0, ota_state = 1
08-27 08:31:35.001   731   745 V mpu_uart:  >> log: 
08-27 08:31:35.001   731   731 I mpu_uart: [TIME-:126]:delete
08-27 08:31:35.015  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.016   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:31:35.016   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x35, 0x38, 0x20, 0x6d, 0x76, 0xa, 0x71, ]
08-27 08:31:35.017   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12358 mv
08-27 08:31:35.017   731   745 V mpu_uart:  >> log: 
08-27 08:31:35.021   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:31:35.021   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:31:35.030   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:31:35.030   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:31:35.030   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:31:35.030   731   745 V mpu_uart:  >> log: 
08-27 08:31:35.031   731   747 I mpu_uart: [TIME-:127]:delete
08-27 08:31:35.032  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.035  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:31:35.035  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:31:35.036   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:35.036   731   747 I mpu_uart: [TIME-:128]:create
08-27 08:31:35.040   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:35.040   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:31:35.040   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:35.041  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:31:35.041  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:31:35.041  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:31:35.049  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.050   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:31:35.050   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:31:35.051   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:31:35.051   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x38, 0xa3, ]
08-27 08:31:35.051   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:35.051   731   747 I mpu_uart: [TIME-:128]:delete
08-27 08:31:35.052  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x38 
08-27 08:31:35.052  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:31:35.052  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:31:35.052  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12344
08-27 08:31:35.052  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12344
08-27 08:31:35.061   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:31:35.062   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 08:31:35.062   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:35.063  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 08:31:35.063  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:31:35.063  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:31:35.063  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 08:31:35.063  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
08-27 08:31:35.063  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 08:31:35.064  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:31:35.066  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.083  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.100  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.117  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.229  1033  1504 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:31:35.542  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:31:35.543  2543  2743 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:31:35.545  3511  3822 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:31:35.546  3511  4058 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:31:35.546  3511  4058 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:31:35.546  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:31:35.547  2543  2743 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:31:35.547  2543  2743 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:31:35.547  3511  4058 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@c5c5f6
08-27 08:31:35.547  3511  4058 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:31:35.547  2543  2743 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:31:35.547  3511  4058 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:31:35.548   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:31:35.548   731   747 I mpu_uart: [TIME-:129]:create
08-27 08:31:35.552  1057  3765 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:31:35.552  1057  3765 E ActivityManager: java.lang.Throwable
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:31:35.552  1057  3765 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:31:35.558   731   744 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:31:35.558   731   744 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:31:35.562   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:35.562   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:31:35.562   731   747 I mpu_uart: [TIME-:129]:delete
08-27 08:31:35.562   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:31:35.563  2543  2743 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:31:35.564   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:35.564  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:31:35.564   731   731 I mpu_uart: [TIME-:130]:create
08-27 08:31:35.564  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:31:35.564  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:31:35.582   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:35.583   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:31:36.053  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:31:36.053  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:31:36.054  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:31:36.054   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:36.054   731   747 I mpu_uart: [TIME-:131]:create
08-27 08:31:36.563   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:31:36.583   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:31:36.583   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:36.583   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:31:37.564   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:31:37.584   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:31:37.584   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:37.584   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
