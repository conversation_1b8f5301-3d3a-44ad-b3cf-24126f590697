// Copyright 2010 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

package {
    default_applicable_licenses: ["frameworks_av_camera_license"],
}

// Added automatically by a large-scale-change that took the approach of
// 'apply every license found to every target'. While this makes sure we respect
// every license restriction, it may not be entirely correct.
//
// e.g. GPL in an MIT project might only apply to the contrib/ directory.
//
// Please consider splitting the single license below into multiple licenses,
// taking care not to lose any license_kind information, and overriding the
// default license using the 'licenses: [...]' property on targets as needed.
//
// For unused files, consider creating a 'fileGroup' with "//visibility:private"
// to attach the license to, and including a comment whether the files may be
// used in the current project.
// See: http://go/android-license-faq
license {
    name: "frameworks_av_camera_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
        "SPDX-license-identifier-MIT",
        "SPDX-license-identifier-Unicode-DFS",
    ],
    license_text: [
        "NOTICE",
    ],
}

cc_library_headers {
    name: "camera_headers",
    export_include_dirs: ["include"],
}
cc_library_shared {
    name: "libcamera_client",

    aidl: {
        export_aidl_headers: true,
        local_include_dirs: ["aidl"],
        include_dirs: [
            "frameworks/native/aidl/gui",
        ],
    },

    srcs: [
        // AIDL files for camera interfaces
        // The headers for these interfaces will be available to any modules that
        // include libcamera_client, at the path "aidl/package/path/BnFoo.h"
        ":libcamera_client_aidl",

        // Source for camera interface parcelables, and manually-written interfaces
        "Camera.cpp",
        "CameraMetadata.cpp",
        "CameraParameters.cpp",
        "CaptureResult.cpp",
        "CameraParameters2.cpp",
        "CameraSessionStats.cpp",
        "ICamera.cpp",
        "ICameraClient.cpp",
        "ICameraRecordingProxy.cpp",
        "camera2/CaptureRequest.cpp",
        "camera2/ConcurrentCamera.cpp",
        "camera2/OutputConfiguration.cpp",
        "camera2/SessionConfiguration.cpp",
        "camera2/SubmitInfo.cpp",
        "CameraBase.cpp",
        "CameraUtils.cpp",
        "VendorTagDescriptor.cpp",
    ],

    shared_libs: [
        "libbase",
        "libcutils",
        "libutils",
        "liblog",
        "libbinder",
        "libgui",
        "libcamera_metadata",
        "libnativewindow",
        "lib-platform-compat-native-api",
    ],

    include_dirs: [
        "system/media/private/camera/include",
        "frameworks/native/include/media/openmax",
    ],
    export_include_dirs: [
         "include",
         "include/camera"
    ],
    export_shared_lib_headers: ["libcamera_metadata", "libnativewindow", "libgui"],

    cflags: [
        "-Werror",
        "-Wall",
        "-Wextra",
    ],

}

cc_library_host_static {
    name: "libcamera_client_host",

    srcs: [
        "CameraMetadata.cpp",
        "VendorTagDescriptor.cpp",
    ],

    shared_libs: [
        "libbase",
        "libcamera_metadata",
    ],

    include_dirs: [
        "system/media/private/camera/include",
        "frameworks/native/include/media/openmax",
    ],

    export_include_dirs: [
        "include",
        "include/camera"
    ],
}

// AIDL interface between camera clients and the camera service.
filegroup {
    name: "libcamera_client_aidl",
    srcs: [
        "aidl/android/hardware/ICameraService.aidl",
        "aidl/android/hardware/ICameraServiceListener.aidl",
        "aidl/android/hardware/ICameraServiceProxy.aidl",
        "aidl/android/hardware/camera2/ICameraDeviceCallbacks.aidl",
        "aidl/android/hardware/camera2/ICameraDeviceUser.aidl",
        "aidl/android/hardware/camera2/ICameraOfflineSession.aidl",
        "aidl/android/hardware/camera2/ICameraInjectionCallback.aidl",
        "aidl/android/hardware/camera2/ICameraInjectionSession.aidl",
    ],
    path: "aidl",
}

// Extra AIDL files that are used by framework.jar but not libcamera_client
// because they have hand-written native implementations.
filegroup {
    name: "libcamera_client_framework_aidl",
    srcs: [
        "aidl/android/hardware/ICamera.aidl",
        "aidl/android/hardware/ICameraClient.aidl",
    ],
    path: "aidl",
}
