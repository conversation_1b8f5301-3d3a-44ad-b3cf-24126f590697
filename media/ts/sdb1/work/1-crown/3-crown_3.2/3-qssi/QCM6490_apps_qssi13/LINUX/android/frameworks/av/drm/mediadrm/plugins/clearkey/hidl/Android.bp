//
// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

// *** THIS PACKAGE HAS SPECIAL LICENSING CONDITIONS.  PLEASE
//     CONSULT THE <NAME_EMAIL> BEFORE
//     DEPENDING ON IT IN YOUR PROJECT. ***
package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    //   legacy_by_exception_only (by exception only)
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_defaults {
    name: "clearkey_service_defaults",
    vendor: true,

    srcs: [
        "AesCtrDecryptor.cpp",
        "Base64.cpp",
        "Buffer.cpp",
        "CreatePluginFactories.cpp",
        "CryptoFactory.cpp",
        "CryptoPlugin.cpp",
        "DeviceFiles.cpp",
        "DrmFactory.cpp",
        "DrmPlugin.cpp",
        "InitDataParser.cpp",
        "JsonWebKey.cpp",
        "MemoryFileSystem.cpp",
        "Session.cpp",
        "SessionLibrary.cpp",
    ],

    relative_install_path: "hw",

    cflags: ["-Wall", "-Werror", "-Wthread-safety"],

    shared_libs: [
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "android.hardware.drm@1.3",
        "android.hardware.drm@1.4",
        "libbase",
        "libbinder",
        "libcrypto",
        "libhidlbase",
        "libhidlmemory",
        "liblog",
        "libprotobuf-cpp-lite",
        "libutils",
    ],

    static_libs: [
        "libclearkeycommon",
        "libclearkeydevicefiles-protos",
        "libjsmn",
    ],

    local_include_dirs: ["include"],

    export_static_lib_headers: ["libjsmn"],

    sanitize: {
        integer_overflow: true,
    },
}
cc_library_static {
    name: "libclearkeydevicefiles-protos",
    vendor: true,

    proto: {
        export_proto_headers: true,
        type: "lite",
    },
    srcs: ["protos/DeviceFiles.proto"],
}

cc_library {
    name: "libclearkeyhidl",
    defaults: ["clearkey_service_defaults"],
}

cc_binary {
    name: "<EMAIL>",
    defaults: ["clearkey_service_defaults"],
    srcs: ["service.cpp"],
    init_rc: ["<EMAIL>"],
    vintf_fragments: ["<EMAIL>"],
}

cc_binary {
    name: "<EMAIL>",
    overrides: ["<EMAIL>"],
    defaults: ["clearkey_service_defaults"],
    srcs: ["serviceLazy.cpp"],
    init_rc: ["<EMAIL>"],
    vintf_fragments: ["<EMAIL>"],
}

cc_binary {
    name: "<EMAIL>",
    defaults: ["clearkey_service_defaults"],
    srcs: ["service.cpp"],
    init_rc: ["<EMAIL>"],
    vintf_fragments: ["<EMAIL>"],
}

cc_binary {
    name: "<EMAIL>",
    overrides: ["<EMAIL>"],
    defaults: ["clearkey_service_defaults"],
    srcs: ["serviceLazy.cpp"],
    init_rc: ["<EMAIL>"],
    vintf_fragments: ["<EMAIL>"],
}

cc_fuzz {
    name: "clearkeyV1.4_fuzzer",
    vendor: true,
    srcs: [
        "fuzzer/clearkeyV1.4_fuzzer.cpp",
    ],
    static_libs: [
        "libclearkeyhidl",
        "libclearkeycommon",
        "libclearkeydevicefiles-protos",
        "libjsmn",
        "libprotobuf-cpp-lite",
    ],
    shared_libs: [
        "android.hidl.allocator@1.0",
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "android.hardware.drm@1.3",
        "android.hardware.drm@1.4",
        "libcrypto",
        "libhidlbase",
        "libhidlmemory",
        "liblog",
        "libutils",
    ],
    fuzz_config: {
        cc: [
            "<EMAIL>",
        ],
        componentid: 155276,
    },
}
