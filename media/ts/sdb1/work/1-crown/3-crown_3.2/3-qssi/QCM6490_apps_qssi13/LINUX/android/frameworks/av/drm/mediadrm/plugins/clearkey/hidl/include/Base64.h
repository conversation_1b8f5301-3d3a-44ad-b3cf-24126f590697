/*
 * Copyright (C) 2018 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef BASE_64_H_

#define BASE_64_H_

#include <android/hardware/drm/1.0/types.h>

#include "Buffer.h"

namespace android {
namespace hardware {
namespace drm {
namespace V1_4 {
namespace clearkey {

using ::android::sp;

struct Buffer;

sp<Buffer> decodeBase64(const std::string &s);
void encodeBase64(const void *data, size_t size, std::string *out);

void encodeBase64Url(const void *data, size_t size, std::string *out);

} // namespace clearkey
} // namespace V1_4
} // namespace drm
} // namespace hardware
} // namespace android

#endif  // BASE_64_H_
