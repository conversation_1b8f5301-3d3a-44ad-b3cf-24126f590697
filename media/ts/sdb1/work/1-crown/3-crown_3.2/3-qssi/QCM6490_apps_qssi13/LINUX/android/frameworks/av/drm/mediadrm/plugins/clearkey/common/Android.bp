//
// Copyright (C) 2018 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library_static {
    name: "libclearkeycommon",
    vendor: true,

    srcs: [
        "ClearKeyUUID.cpp",
        "Utils.cpp",
    ],

    cflags: ["-Wall", "-Werror"],

    include_dirs: ["frameworks/av/include"],

    shared_libs: ["libutils"],

    export_include_dirs: ["include"],

    sanitize: {
        integer_overflow: true,
    },
}

cc_library_static {
    name: "libclearkeydevicefiles-protos.common",
    vendor: true,

    proto: {
        export_proto_headers: true,
        type: "lite",
    },
    srcs: ["protos/DeviceFiles.proto"],
}

cc_library_static {
    name: "libclearkeybase",
    vendor: true,

    srcs: [
        "AesCtrDecryptor.cpp",
        "Base64.cpp",
        "Buffer.cpp",
        "ClearKeyUUID.cpp",
        "DeviceFiles.cpp",
        "InitDataParser.cpp",
        "JsonWebKey.cpp",
        "MemoryFileSystem.cpp",
        "Session.cpp",
        "SessionLibrary.cpp",
        "Utils.cpp",
    ],

    cflags: ["-Wall", "-Werror"],

    include_dirs: ["frameworks/av/include"],

    shared_libs: [
        "libutils",
        "libcrypto",
    ],

    whole_static_libs: [
        "libjsmn",
        "libclearkeydevicefiles-protos.common",
    ],

    export_include_dirs: [
        "include",
        "include/clearkeydrm",
    ],

    sanitize: {
        integer_overflow: true,
    },
}
