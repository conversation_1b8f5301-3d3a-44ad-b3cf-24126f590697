// Copyright (C) 2020 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// This is the version of the drm metrics configured for protobuf full on host.
// It is used by the metrics_dump tool.

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library_host_shared {
    name: "libdrm_metrics_protos_full_host",
    vendor_available: true,

    include_dirs: ["external/protobuf/src"],

    srcs: [
        "metrics.proto",
    ],

    proto: {
        export_proto_headers: true,
        type: "full",
    },

    cflags: [
        // Suppress unused parameter error. This error occurs
        // when using the map type in a proto definition.
        "-Wno-unused-parameter",
    ],
}
