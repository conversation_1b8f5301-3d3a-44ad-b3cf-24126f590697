# Input Device Configuration file for chsc_cap_touch
# This file corrects multi-touch coordinate mapping issues

# Basic device properties
device.internal = 1

# Touch screen properties
touch.deviceType = touchScreen
touch.orientationAware = 1

# Multi-touch properties
touch.gestureMode = multiTouch
touch.size.calibration = diameter
touch.size.scale = 1.0
touch.size.bias = 0.0

# Pressure calibration
touch.pressure.calibration = amplitude
touch.pressure.scale = 0.01

# Orientation calibration
touch.orientation.calibration = none

# Distance calibration
touch.distance.calibration = none

# Coverage calibration
touch.coverage.calibration = box

# Multi-touch coordinate correction
# Fix for second finger coordinate inversion
touch.size.isSummed = 0

# Coordinate bounds (320x240 display)
touch.size.range = 0.0, 1.0
