//
// libmediadrm
//

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library_headers {
    name: "libmediadrm_headers",

    export_include_dirs: [
        "interface"
    ],

}

cc_library {
    name: "libmediadrm",

    srcs: [
        "DrmPluginPath.cpp",
        "DrmSessionManager.cpp",
        "SharedLibrary.cpp",
        "DrmHal.cpp",
        "DrmHalHidl.cpp",
        "DrmHalAidl.cpp",
        "CryptoHal.cpp",
        "CryptoHalHidl.cpp",
        "CryptoHalAidl.cpp",
        "DrmUtils.cpp",
        "DrmHalListener.cpp",
    ],

    local_include_dirs: [
        "include",
        "interface"
    ],

    export_include_dirs: [
        "include"
    ],

    header_libs: [
        "libmedia_headers",
    ],

    shared_libs: [
        "libbinder_ndk",
        "libcutils",
        "libdl",
        "liblog",
        "libmedia",
        "libmediadrmmetrics_lite",
        "libmediametrics#1",
        "libmediautils",
        "libstagefright_foundation",
        "libutils",
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "android.hardware.drm@1.3",
        "android.hardware.drm@1.4",
        "libhidlallocatorutils",
        "libhidlbase",
        "android.hardware.drm-V1-ndk",
    ],

    static_libs: [
        "resourcemanager_aidl_interface-ndk",
        "libaidlcommonsupport",
    ],

    export_shared_lib_headers: [
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "android.hardware.drm@1.4",
    ],

    cflags: [
        "-Werror",
        "-Wall",
    ],
}

// This is the version of the drm metrics configured for protobuf lite.
cc_library_shared {
    name: "libmediadrmmetrics_lite",
    srcs: [
        "DrmMetrics.cpp",
        "PluginMetricsReporting.cpp",
        "protos/metrics.proto",
    ],

    local_include_dirs: [
        "include"
    ],

    proto: {
        export_proto_headers: true,
        type: "lite",
    },
    header_libs: [
        "libmedia_headers",
    ],
    shared_libs: [
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "liblog",
        "libmediametrics",
        "libprotobuf-cpp-lite",
        "libutils",
    ],
    cflags: [
        // Suppress unused parameter and no error options. These cause problems
        // with the when using the map type in a proto definition.
        "-Wno-unused-parameter",
    ],
}

// This is the version of the drm metrics library configured for full protobuf.
cc_library_shared {
    name: "libmediadrmmetrics_full",
    srcs: [
        "DrmMetrics.cpp",
        "PluginMetricsReporting.cpp",
        "protos/metrics.proto",
    ],

    local_include_dirs: [
        "include"
    ],

    proto: {
        export_proto_headers: true,
        type: "full",
    },
    header_libs: [
        "libmedia_headers",
    ],
    shared_libs: [
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "libbase",
        "liblog",
        "libmediametrics",
        "libprotobuf-cpp-full",
        "libstagefright_foundation",
        "libutils",
    ],
    cflags: [
        // Suppress unused parameter and no error options. These cause problems
        // when using the map type in a proto definition.
        "-Wno-unused-parameter",
    ],
}

cc_library_shared {
    name: "libmediadrmmetrics_consumer",
    srcs: [
        "DrmMetricsConsumer.cpp",
    ],

    shared_libs: [
        "android.hardware.drm@1.0",
        "android.hardware.drm@1.1",
        "android.hardware.drm@1.2",
        "libbinder",
        "libhidlbase",
        "liblog",
        "libmediadrm",
        "libmediadrmmetrics_full",
        "libutils",
    ],

    header_libs: [
        "libmediametrics_headers",
        "libstagefright_foundation_headers",
        "libmedia_headers",
    ],
}
