//
// Copyright (C) 2014 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.
//

package {
    // See: http://go/android-license-faq
    // A large-scale-change added 'default_applicable_licenses' to import
    // all of the 'license_kinds' from "frameworks_av_license"
    // to get the below license kinds:
    //   SPDX-license-identifier-Apache-2.0
    default_applicable_licenses: ["frameworks_av_license"],
}

cc_library_shared {
    name: "libdrmclearkeyplugin",
    vendor: true,

    srcs: [
        "AesCtrDecryptor.cpp",
        "CreatePluginFactories.cpp",
        "CryptoFactory.cpp",
        "CryptoPlugin.cpp",
        "DrmFactory.cpp",
        "DrmPlugin.cpp",
        "InitDataParser.cpp",
        "JsonWebKey.cpp",
        "Session.cpp",
        "SessionLibrary.cpp",
    ],

    relative_install_path: "mediadrm",

    cflags: ["-Wall", "-Werror"],

    shared_libs: [
        "libcrypto",
        "liblog",
        "libstagefright_foundation",
        "libutils",
    ],

    static_libs: [
        "libclearkeycommon",
        "libjsmn"
    ],

    local_include_dirs: ["include"],
    export_include_dirs: ["include"],
    export_static_lib_headers: ["libjsmn"],

    include_dirs: [
        "frameworks/native/include",
        "frameworks/av/include",
    ],

    sanitize: {
        integer_overflow: true,
    },
}
