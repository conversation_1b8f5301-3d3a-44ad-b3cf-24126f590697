/*
 * Copyright (C) 2014 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef CLEARKEY_UTILS_H_
#define CLEARKEY_UTILS_H_

#include <string>
#include <utils/Vector.h>

namespace android {
// Add a comparison operator for this Vector specialization so that it can be
// used as a key in a KeyedVector.
bool operator<(const Vector<uint8_t> &lhs, const Vector<uint8_t> &rhs);

std::string ByteArrayToHexString(const uint8_t* in_buffer, size_t length);

} // namespace android

#define UNUSED(x) (void)(x);

#endif // CLEARKEY_UTILS_H_
