/*
 * Copyright (C) 2017 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */

#ifndef MOCK_CAS_SESSION_LIBRARY_H_
#define MOCK_CAS_SESSION_LIBRARY_H_

#include <media/cas/CasAPI.h>
#include <media/stagefright/foundation/ABase.h>
#include <utils/KeyedVector.h>
#include <utils/Mutex.h>
#include <utils/RefBase.h>

namespace android {

class MockCasSession : public RefBase {
public:
    explicit MockCasSession(CasPlugin *plugin) : mPlugin(plugin) {}
    virtual ~MockCasSession() {}

private:
    friend class MockSessionLibrary;

    CasPlugin* mPlugin;

    CasPlugin* getPlugin() const { return mPlugin; }

    DISALLOW_EVIL_CONSTRUCTORS(MockCasSession);
};

class MockSessionLibrary {
public:
    static MockSessionLibrary* get();

    status_t addSession(CasPlugin *plugin, CasSessionId *sessionId);

    sp<MockCasSession> findSession(const CasSessionId& sessionId);

    void destroySession(const CasSessionId& sessionId);

    void destroyPlugin(CasPlugin *plugin);

private:
    static Mutex sSingletonLock;
    static MockSessionLibrary* sSingleton;

    Mutex mSessionsLock;
    uint32_t mNextSessionId;
    KeyedVector<CasSessionId, sp<MockCasSession> > mIDToSessionMap;

    MockSessionLibrary();
    DISALLOW_EVIL_CONSTRUCTORS(MockSessionLibrary);
};
} // namespace android

#endif // MOCK_CAS_SESSION_LIBRARY_H_
