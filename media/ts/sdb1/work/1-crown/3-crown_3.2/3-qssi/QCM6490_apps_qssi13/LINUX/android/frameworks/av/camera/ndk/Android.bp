// Copyright (C) 2016 The Android Open Source Project
//
// Licensed under the Apache License, Version 2.0 (the "License");
// you may not use this file except in compliance with the License.
// You may obtain a copy of the License at
//
//      http://www.apache.org/licenses/LICENSE-2.0
//
// Unless required by applicable law or agreed to in writing, software
// distributed under the License is distributed on an "AS IS" BASIS,
// WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
// See the License for the specific language governing permissions and
// limitations under the License.

// Headers module is in frameworks/av/Android.bp because modules are not allowed
// to refer to headers in parent directories and the headers live in
// frameworks/av/include.

package {
    default_applicable_licenses: ["frameworks_av_camera_ndk_license"],
}

// Added automatically by a large-scale-change that took the approach of
// 'apply every license found to every target'. While this makes sure we respect
// every license restriction, it may not be entirely correct.
//
// e.g. GPL in an MIT project might only apply to the contrib/ directory.
//
// Please consider splitting the single license below into multiple licenses,
// taking care not to lose any license_kind information, and overriding the
// default license using the 'licenses: [...]' property on targets as needed.
//
// For unused files, consider creating a 'fileGroup' with "//visibility:private"
// to attach the license to, and including a comment whether the files may be
// used in the current project.
// See: http://go/android-license-faq
license {
    name: "frameworks_av_camera_ndk_license",
    visibility: [":__subpackages__"],
    license_kinds: [
        "SPDX-license-identifier-Apache-2.0",
        "SPDX-license-identifier-MIT",
        "SPDX-license-identifier-Unicode-DFS",
    ],
    license_text: [
        "NOTICE",
    ],
}

ndk_library {
    name: "libcamera2ndk",
    symbol_file: "libcamera2ndk.map.txt",
    first_version: "24",
    unversioned_until: "current",
}

ndk_headers {
    name: "libcamera2ndk_headers",
    from: "include/camera",
    to: "camera",
    srcs: ["include/camera/**/*.h"],
    license: "NOTICE",
}

cc_library_shared {
    name: "libcamera2ndk",
    srcs: [
        "NdkCameraManager.cpp",
        "NdkCameraMetadata.cpp",
        "NdkCameraDevice.cpp",
        "NdkCaptureRequest.cpp",
        "NdkCameraCaptureSession.cpp",
        "impl/ACameraManager.cpp",
        "impl/ACameraMetadata.cpp",
        "impl/ACameraDevice.cpp",
        "impl/ACameraCaptureSession.cpp",
    ],
    shared_libs: [
        "libbinder",
        "liblog",
        "libgui",
        "libutils",
        "libandroid_runtime",
        "libcamera_client",
        "libstagefright_foundation",
        "libcutils",
        "libcamera_metadata",
        "libmediandk",
        "libnativewindow",
    ],
    header_libs: [
        "jni_headers",
    ],
    cflags: [
        "-fvisibility=hidden",
        "-DEXPORT=__attribute__((visibility(\"default\")))",
        "-Wall",
        "-Wextra",
        "-Werror",
    ],
    // TODO: jchowdhary@, use header_libs instead b/131165718
    include_dirs: [
        "system/media/private/camera/include",
    ],
    export_include_dirs: ["include"],
    export_shared_lib_headers: [
        "libnativewindow",
    ],
    version_script: "libcamera2ndk.map.txt",
    sdclang: false, // See b/16384269
}

cc_library_shared {
    name: "libcamera2ndk_vendor",
    vendor: true,
    srcs: [
        "ndk_vendor/impl/ACameraDevice.cpp",
        "ndk_vendor/impl/ACameraManager.cpp",
        "ndk_vendor/impl/utils.cpp",
        "impl/ACameraMetadata.cpp",
        "impl/ACameraCaptureSession.cpp",
        "NdkCameraMetadata.cpp",
        "NdkCameraCaptureSession.cpp",
        "NdkCameraManager.cpp",
        "NdkCameraDevice.cpp",
        "NdkCaptureRequest.cpp",
    ],

    export_include_dirs: ["include"],
    export_shared_lib_headers: [
        "libcutils",
    ],
    local_include_dirs: [
        ".",
        "include",
        "impl",
    ],
    cflags: [
        "-fvisibility=hidden",
        "-DEXPORT=__attribute__((visibility(\"default\")))",
        "-D__ANDROID_VNDK__",
    ],

    shared_libs: [
        "libfmq",
        "libhidlbase",
        "libhardware",
        "libnativewindow",
        "liblog",
        "libutils",
        "libstagefright_foundation",
        "libcutils",
        "libcamera_metadata",
        "libmediandk",
        "android.frameworks.cameraservice.device@2.0",
        "android.frameworks.cameraservice.device@2.1",
        "android.frameworks.cameraservice.common@2.0",
        "android.frameworks.cameraservice.service@2.0",
        "android.frameworks.cameraservice.service@2.1",
        "android.frameworks.cameraservice.service@2.2",
    ],
    static_libs: [
        "android.hardware.camera.common@1.0-helper",
        "libarect",
    ],
    // TODO: jchowdhary@, use header_libs instead b/131165718
    include_dirs: [
        "system/media/private/camera/include",
    ],
    product_variables: {
        pdk: {
            enabled: false,
        },
    },
    sdclang: false, // See b/163842697
}

cc_test {
    name: "ACameraNdkVendorTest",
    vendor: true,
    srcs: [
        "ndk_vendor/tests/AImageReaderVendorTest.cpp",
        "ndk_vendor/tests/ACameraManagerTest.cpp",
    ],
    shared_libs: [
        "libcamera2ndk_vendor",
        "libcamera_metadata",
        "libmediandk",
        "libnativewindow",
        "libutils",
        "libui",
        "libcutils",
        "liblog",
    ],
    static_libs: [
        "android.hardware.camera.common@1.0-helper",
    ],
    cflags: [
        "-D__ANDROID_VNDK__",
    ],
}
