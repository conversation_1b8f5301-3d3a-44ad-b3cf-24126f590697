/*
 * Copyright (C) 2021 The Android Open Source Project
 *
 * Licensed under the Apache License, Version 2.0 (the "License");
 * you may not use this file except in compliance with the License.
 * You may obtain a copy of the License at
 *
 *      http://www.apache.org/licenses/LICENSE-2.0
 *
 * Unless required by applicable law or agreed to in writing, software
 * distributed under the License is distributed on an "AS IS" BASIS,
 * WITHOUT WARRANTIES OR CONDITIONS OF ANY KIND, either express or implied.
 * See the License for the specific language governing permissions and
 * limitations under the License.
 */
syntax = "proto2";

package clearkeydrm;

// need this if we are using libprotobuf-cpp-2.3.0-lite
option optimize_for = LITE_RUNTIME;

message License {
  enum LicenseState {
    ACTIVE = 1;
    RELEASING = 2;
  }

  optional LicenseState state = 1;
  optional bytes license = 2;
}

message OfflineFile {
  enum FileType {
    LICENSE = 1;
  }

  enum FileVersion {
    VERSION_1 = 1;
  }

  optional FileType type = 1;
  optional FileVersion version = 2 [default = VERSION_1];
  optional License license = 3;

}

message HashedFile {
  optional bytes file = 1;
  // A raw (not hex-encoded) SHA256, taken over the bytes of 'file'.
  optional bytes hash = 2;
}
