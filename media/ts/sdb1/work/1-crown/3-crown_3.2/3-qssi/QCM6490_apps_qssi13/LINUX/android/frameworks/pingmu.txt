--------- beginning of main
08-27 08:29:56.790  1057  2672 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.790  1057  2672 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.791  1057  2672 D CompatibilityInfo: applicationScale - 1.0
--------- beginning of system
08-27 08:29:56.811  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.814  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.815  1057  2672 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.815  1057  2672 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.815  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.815  1057  2672 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:56.816  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.817  1057  2672 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.817  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.817  1057  2672 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.817  1057  2672 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:56.818  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.819  3422  3422 W FotaClient_BootReceiver: onReceive: action = android.intent.action.BOOT_COMPLETED
08-27 08:29:56.823  1057  2672 D CompatibilityChangeReporter: Compat change id reported: 168419799; UID 10084; state: DISABLED
08-27 08:29:56.823  1057  2672 D CompatibilityChangeReporter: Compat change id reported: 273564678; UID 10084; state: DISABLED
08-27 08:29:56.824  1057  2672 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.824  1057  2672 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.824  1057  2672 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:56.828  4631  4652 I Process : Sending signal. PID: 4631 SIG: 9
08-27 08:29:56.831  2543  2543 I McuOtaServiceApp_BootCompleteReceiver: onReceive: action = android.intent.action.BOOT_COMPLETED
08-27 08:29:56.834  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 168419799; UID 10140; state: DISABLED
08-27 08:29:56.834  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 273564678; UID 10140; state: DISABLED
08-27 08:29:56.834  1057  3252 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.834  1057  3252 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.834  1057  3252 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:56.839  4036  4036 I Dialer  : VoicemailModule.provideVoicemailClient - providing VoicemailClientImpl
08-27 08:29:56.840  4036  4036 I Dialer  : VvmOmtpService - onBoot
08-27 08:29:56.841  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.844  1057  3252 D ConnectivityService: requestNetwork for uid/pid:1000/1057 asUid: 10140 activeRequest: null callbackRequest: 47 [NetworkRequest [ REQUEST id=48, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10140 RequestorUid: 1000 RequestorPkg: android UnderlyingNetworks: Null] ]] callback flags: 0 order: 2147483647
08-27 08:29:56.844  1057  2080 D ConnectivityService: NetReassign [no changes]
08-27 08:29:56.846  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.846  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.846  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.847  1057  2058 D WifiNetworkFactory: got request NetworkRequest [ REQUEST id=48, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10140 RequestorUid: 1000 RequestorPkg: android UnderlyingNetworks: Null] ]
08-27 08:29:56.847  1057  2058 D UntrustedWifiNetworkFactory: got request NetworkRequest [ REQUEST id=48, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10140 RequestorUid: 1000 RequestorPkg: android UnderlyingNetworks: Null] ]
08-27 08:29:56.847  1057  2058 D OemPaidWifiNetworkFactory: got request NetworkRequest [ REQUEST id=48, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10140 RequestorUid: 1000 RequestorPkg: android UnderlyingNetworks: Null] ]
08-27 08:29:56.847  1057  2058 D MultiInternetWifiNetworkFactory: got request NetworkRequest [ REQUEST id=48, [ Capabilities: INTERNET&NOT_RESTRICTED&TRUSTED&NOT_VCN_MANAGED Uid: 10140 RequestorUid: 1000 RequestorPkg: android UnderlyingNetworks: Null] ]
08-27 08:29:56.848  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.848  1057  3252 W BroadcastQueue: Permission Denial: receiving Intent { act=android.intent.action.BOOT_COMPLETED flg=0x89000010 (has extras) } to org.codeaurora.snapcam/com.android.camera.DisableCameraReceiver requires android.permission.RECEIVE_BOOT_COMPLETED due to sender null (uid 1000)
08-27 08:29:56.850  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.851  1057  1363 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.851  1057  1363 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.851  1057  1363 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:56.855  1057  3252 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:56.855  1057  3252 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:56.855  1057  3252 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:56.863  1057  3252 I ActivityManager: Finished processing BOOT_COMPLETED for u0
08-27 08:29:56.863  1057  3252 I BroadcastQueue: BOOT_COMPLETED_BROADCAST_COMPLETION_LATENCY_REPORTED action:android.intent.action.BOOT_COMPLETED dispatchLatency:1356 completeLatency:6676 dispatchRealLatency:1356 completeRealLatency:6676 receiversSize:77 userId:0 userType:android.os.usertype.full.SYSTEM
--------- beginning of kernel
01-02 03:42:56.715   538   538 I binder  : undelivered transaction 57873, process died.
08-27 08:29:56.867  1057  3765 I ActivityManager: Process com.quicinc.voice.activation (pid 4631) has died: fg  SVC 
08-27 08:29:56.868  1057  1376 I libprocessgroup: Successfully killed process cgroup uid 10137 pid 4631 in 0ms
08-27 08:29:56.869   761   761 I Zygote  : Process 4631 exited due to signal 9 (Killed)
08-27 08:29:56.874  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.876  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.876  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.877  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.878  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.880  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.904  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.906  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.907  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.907  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.908  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:56.756   580   580 I logd    : logdr: UID=2000 GID=2000 PID=4661 b tail=0 logMask=99 pid=0 start=0ns deadline=0ns
08-27 08:29:56.910  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.935  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.937  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.937  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.938  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.938  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.940  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.965  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.967  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.968  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.968  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.969  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.971  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.996  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.997  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.998  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.998  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.998   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:29:56.999   731   747 I mpu_uart: [TIME-:15]:delete
08-27 08:29:56.999   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x32, 0x37, 0x39, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x25, ]
08-27 08:29:56.999   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20279,current_state=0, ota_state = 1
08-27 08:29:56.999   731   745 V mpu_uart:  >> log: 
08-27 08:29:56.999  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:56.996  1598  1598 I tlog    : type=1400 audit(0.0:147): avc: denied { rename } for name="-00001_persist_00018_250827_082953.log.ing" dev="dm-6" ino=14652 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:29:57.001  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.000  1598  1598 I tlog    : type=1400 audit(0.0:148): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
08-27 08:29:57.004   578   578 W hwservicemanage: type=1400 audit(0.0:149): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:29:57.007  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:29:57.007  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:29:57.007  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:42:56.855   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:42:56.855   578   578 I binder  : send failed reply for transaction 57943 to 1598:1620
08-27 08:29:57.004   578   578 W hwservicemanage: type=1400 audit(0.0:150): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
01-02 03:42:56.857   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:42:56.857   578   578 I binder  : send failed reply for transaction 57947 to 1598:1620
08-27 08:29:57.008  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:29:57.008  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:29:57.008  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:29:57.015   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:29:57.015   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x34, 0x34, 0x20, 0x6d, 0x76, 0xa, 0x74, ]
08-27 08:29:57.015   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12344 mv
08-27 08:29:57.015   731   745 V mpu_uart:  >> log: 
08-27 08:29:57.019   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:29:57.019   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:29:57.016  1598  1598 I tlog    : type=1400 audit(0.0:151): avc: denied { setattr } for name="-00001_persist_00019_250827_082957.log.ing" dev="dm-6" ino=14664 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:29:57.026  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.027   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:29:57.027  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.028   731   731 I mpu_uart: [TIME-:16]:delete
08-27 08:29:57.028   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:29:57.028   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:29:57.028   731   745 V mpu_uart:  >> log: 
08-27 08:29:57.028  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:29:57.028  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:29:57.028  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.028  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.029   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:29:57.029   731   747 I mpu_uart: [TIME-:17]:create
08-27 08:29:57.029  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.032  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.038   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:29:57.038   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:29:57.038   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:29:57.038  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:29:57.039  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:29:57.039  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:29:57.048   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:29:57.048   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:29:57.048   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:29:57.048   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x1b, 0x80, ]
08-27 08:29:57.048   731   747 I mpu_uart: [TIME-:17]:delete
08-27 08:29:57.048   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:29:57.049  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x1B 
08-27 08:29:57.049  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:29:57.049  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:29:57.049  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12315
08-27 08:29:57.049  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12315
08-27 08:29:57.056  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.058  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.059  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.059  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.059   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:29:57.059   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 08:29:57.059   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:29:57.060  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.060  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 08:29:57.060  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:29:57.060  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:29:57.060  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 08:29:57.060  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 08:29:57.061  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 08:29:57.062  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.066  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:29:57.086  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.088  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.089  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.089  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.090  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.092  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.117  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.118  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.119  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.119  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.120  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.122  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:56.983     7     7 I camera0_vana_ldo: disabling
01-02 03:42:56.983     7     7 I camera0_vio_ldo: disabling
01-02 03:42:56.983     7     7 I camera0_vdig_ldo: disabling
01-02 03:42:56.983     7     7 I camera1_vana_ldo: disabling
01-02 03:42:56.983     7     7 I camera1_vio_ldo: disabling
01-02 03:42:56.983     7     7 I camera1_vdig_ldo: disabling
01-02 03:42:56.983     7     7 I camera2_vdd12: disabling
01-02 03:42:56.983     7     7 I camera2_vdd18: disabling
01-02 03:42:56.983     7     7 I camera2_vdd33: disabling
08-27 08:29:57.147  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.148  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.150  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.150  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.151  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.152  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.178  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.179  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.180  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.180  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.181  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.183  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.208  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.209  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.210  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.210  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.211  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.213  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.239  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.239  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.241  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.241  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.241  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.243  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.269  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.269  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.271  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.271  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.272  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.273  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.300  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.300  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.301  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.301  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.302  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.304  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.330  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.330  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.331  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.331  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.332  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.334  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.360  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.360  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.362  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.362  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.363  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.364  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.364   904   930 E ssgtzd  : Modemcomm: Failed to get handle of QMSCA service
08-27 08:29:57.365   904   930 I ssgtzd  : SecureChannel: Secure Channel created successfully
08-27 08:29:57.366   904   930 I ssgtzd  : SecureChannel: Wait on signal for message in queue
08-27 08:29:57.372   904  4664 I ssgtzd  : RticThread: Attempt: 1
08-27 08:29:57.375   904  4664 I ssgtzd  : RticThread: Successfully got QWES service handle
08-27 08:29:57.379   631   640 D DrmLibTime: got the req here! ret=0
08-27 08:29:57.379   631   640 D DrmLibTime: command id, time_cmd_id = 770
08-27 08:29:57.379   631   640 D DrmLibTime: time_getutcsec starts!
08-27 08:29:57.379   631   640 D DrmLibTime: QSEE Time Listener: time_getutcsec
08-27 08:29:57.379   631   640 D DrmLibTime: QSEE Time Listener: get_utc_seconds
08-27 08:29:57.379   631   640 D DrmLibTime: QSEE Time Listener: time_get_modem_time
08-27 08:29:57.379   631   640 D DrmLibTime: QSEE Time Listener: Checking if ATS_MODEM is set or not.
08-27 08:29:57.379   631   640 D QC-time-services: Lib:time_genoff_operation: pargs->base = 13
08-27 08:29:57.379   631   640 D QC-time-services: Lib:time_genoff_operation: pargs->operation = 2
08-27 08:29:57.379   631   640 D QC-time-services: Lib:time_genoff_operation: pargs->ts_val = 0
08-27 08:29:57.381   631   640 D QC-time-services: Lib:time_genoff_operation: Send to server  passed!!
08-27 08:29:57.381   711   722 D QC-time-services: Daemon: Connection accepted:time_genoff
08-27 08:29:57.384   711  4681 D QC-time-services: Daemon:Received base = 13, unit = 1, operation = 2,value = 0
08-27 08:29:57.384   711  4681 D QC-time-services: Daemon:genoff_opr: Base = 13, val = 0, operation = 2
08-27 08:29:57.385   711  4681 D QC-time-services: offset is: 0 for base: 13
08-27 08:29:57.386   631   640 E QC-time-services: Receive Passed == base = 13, unit = 1, operation = 2, result = 0
08-27 08:29:57.386   631   640 D DrmLibTime: QSEE Time Listener: ATS_MODEM is not set. Fallback to Android system time.
08-27 08:29:57.386   631   640 D DrmLibTime: QSEE Time Listener: Retrieved Android system time: 1756254597
08-27 08:29:57.386   631   640 D DrmLibTime: time_getutcsec returns 0, sec = 1756254597; nsec = 0
08-27 08:29:57.386   631   640 D DrmLibTime: time_getutcsec finished! 
08-27 08:29:57.386   631   640 D DrmLibTime: iotcl_continue_command finished! and return 0 
08-27 08:29:57.386   631   640 D DrmLibTime: before calling ioctl to read the next time_cmd
08-27 08:29:57.389   711   722 E QC-time-services: Daemon: Time-services: Waiting to acceptconnection
08-27 08:29:57.391  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.391  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.241  4664  4664 W QSEECOM : __qseecom_reentrancy_process_incomplete_cmd: get cback req app_id = 1, resp->data = 0
08-27 08:29:57.392  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.392   904   917 I ssgtzd  : SecureChannel: A msg of size :96 writing into queue
08-27 08:29:57.392   904   930 I ssgtzd  : SecureChannel: successfully read a message - dispatch it
08-27 08:29:57.392  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.393  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.394  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.396   904   925 I ssgtzd  : SecureChannel: A msg of size :96 writing into queue
08-27 08:29:57.396   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4181 went down
01-02 03:42:57.243   309   309 I (virq   : irq_count)- 3:46344 217:44230 47:18459 298:8809 57:4583 10:4250 313:3600 220:1414 263:1315 221:1142
01-02 03:42:57.243   309   309 I (cpu    : irq_count)- 0:96500 1:6940 2:5956 3:5003 4:6060 5:6039 6:6269 7:7514
01-02 03:42:57.243   309   309 I (ipi    : irq_count)- 0:91198 1:40894 2:0 3:0 4:0 5:47794 6:0
08-27 08:29:57.399   904   919 I ssgtzd  : SecureChannel: A msg of size :96 writing into queue
08-27 08:29:57.403   904   923 I ssgtzd  : SecureChannel: A msg of size :96 writing into queue
08-27 08:29:57.404   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4182 went down
08-27 08:29:57.404   904  4664 I ssgtzd  : RticThread: Intial Trigger to TTime Success
08-27 08:29:57.405   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4183 went down
08-27 08:29:57.421  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.421  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.422  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.423  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.423  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.425  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.451  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.452  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.452  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.453  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.455  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.455  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.482  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.482  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.483  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.483  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.485  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.485  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.507  3430  4079 D BufferPoolAccessor2.0: bufferpool2 0xb4000070f2f672f8 : 0(0 size) total buffers - 0(0 size) used buffers - 1/7 (recycle/alloc) - 6/100 (fetch/transfer)
08-27 08:29:57.507  3430  4079 D BufferPoolAccessor2.0: bufferpool2 0xb4000070f2f69108 : 0(0 size) total buffers - 0(0 size) used buffers - 2/7 (recycle/alloc) - 5/62 (fetch/transfer)
08-27 08:29:57.507  3430  4079 D BufferPoolAccessor2.0: evictor expired: 2, evicted: 2
08-27 08:29:57.512  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.512  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.513  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.513  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.515  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.516  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.540  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:29:57.540  2543  2743 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:29:57.542  3511  3822 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:29:57.542  3511  3807 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:29:57.542  3511  3807 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:29:57.543  3511  3807 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@e0f1817
08-27 08:29:57.543  3511  3807 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:29:57.543  3511  3807 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:29:57.543  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.543  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:29:57.543  2543  2743 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:29:57.543  2543  2743 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:29:57.543  2543  2743 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:29:57.544   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:29:57.544   731   747 I mpu_uart: [TIME-:18]:create
08-27 08:29:57.544  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.544  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.544  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.545  1057  3765 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:29:57.545  1057  3765 E ActivityManager: java.lang.Throwable
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:29:57.545  1057  3765 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:29:57.546  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.546  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.555   731   744 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:29:57.555   731   744 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:29:57.558   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:29:57.558   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:29:57.558   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:29:57.558   731   747 I mpu_uart: [TIME-:18]:delete
08-27 08:29:57.559  2543  2743 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:29:57.559  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:29:57.559   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:29:57.559  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:29:57.559   731   731 I mpu_uart: [TIME-:19]:create
08-27 08:29:57.559  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:29:57.573  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.574  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.574  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.574  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.576  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.576  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.578   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:29:57.578   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:29:57.604  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.604  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.604  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.605  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.606  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.607  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.634  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.635  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.635  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.635  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.637  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.637  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.648  1057  2238 D BufferPoolAccessor2.0: evictor expired: 6, evicted: 0
01-02 03:42:57.495   456   456 I [kworke][0x390163af][08:29:57.646338] wlan: [456:I:HDD] hdd_psoc_idle_timeout_callback: Psoc idle timeout elapsed; starting psoc shutdown
01-02 03:42:57.496   330   330 I [kworke][0x3901913d][08:29:57.646944] wlan: [330:I:PMO] pmo_core_psoc_send_host_wakeup_ind_to_fw: Host wakeup indication sent to fw
08-27 08:29:57.653  1720  2191 D BufferPoolAccessor2.0: bufferpool2 0xb400006e43c63968 : 0(0 size) total buffers - 0(0 size) used buffers - 26/30 (recycle/alloc) - 4/29 (fetch/transfer)
08-27 08:29:57.653  1720  2191 D BufferPoolAccessor2.0: bufferpool2 0xb400006e43c5b7e8 : 0(0 size) total buffers - 0(0 size) used buffers - 25/29 (recycle/alloc) - 4/28 (fetch/transfer)
08-27 08:29:57.653  1720  2191 D BufferPoolAccessor2.0: bufferpool2 0xb400006e43c5a568 : 0(0 size) total buffers - 0(0 size) used buffers - 44/48 (recycle/alloc) - 4/47 (fetch/transfer)
08-27 08:29:57.653  1720  2191 D BufferPoolAccessor2.0: evictor expired: 3, evicted: 3
01-02 03:42:57.504  2143  2143 E [schedu][0x3903f8cd][08:29:57.655151] wlan: [2143:E:SYS] Processing SYS MC STOP
08-27 08:29:57.664  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.665  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.665  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.666  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.667  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.667  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.524   330   330 I [kworke][0x3909fd39][08:29:57.675690] wlan: [330:I:WMI] WMI Stop
01-02 03:42:57.524   330   330 I [kworke][0x3909ff62][08:29:57.675719] wlan: [330:I:HTC] htc_stop: endpoints cleanup
01-02 03:42:57.525   330   330 I [kworke][0x390a0344][08:29:57.675771] wlan: [330:I:HTC] htc_stop: stopping hif layer
08-27 08:29:57.695  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.695  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.695  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.696  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.697  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.697  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.565   330   330 I [kworke][0x3915d935][08:29:57.716170] wlan: [330:I:HTC] htc_stop: flush endpoints Tx lookup queue
01-02 03:42:57.565   330   330 I [kworke][0x3915da8a][08:29:57.716188] wlan: [330:I:HTC] htc_stop: resetting endpoints state
08-27 08:29:57.725  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.726  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.726  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.726  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.727  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.728  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.582   330   330 I [kworke][0x391af330][08:29:57.733583] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 0
01-02 03:42:57.582   330   330 I [kworke][0x391af40f][08:29:57.733595] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 1
01-02 03:42:57.582   330   330 I [kworke][0x391af486][08:29:57.733601] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 2
01-02 03:42:57.582   330   330 I [kworke][0x391af4f1][08:29:57.733606] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 3
01-02 03:42:57.582   330   330 I [kworke][0x391af55c][08:29:57.733612] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 4
01-02 03:42:57.582   330   330 I [kworke][0x391af5c6][08:29:57.733618] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 5
01-02 03:42:57.582   330   330 I [kworke][0x391af62d][08:29:57.733623] wlan: [330:I:HIF] hif_event_history_deinit: SRNG events history de-initialized for group: 6
08-27 08:29:57.756  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.756  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.757  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.757  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.758  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.758  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.770  1798  1839 D LOWI-********.z: [MqClientIpcReciver] FROM: FreeWifiScanObserver
08-27 08:29:57.770  1798  1839 D LOWI-********.z: [MqClientIpcReciver] REQ:  LOWI_CAPABILITY
08-27 08:29:57.771  1798  1839 D LOWI-********.z: [MqClientIpcReciver] TX-ID:  4
08-27 08:29:57.771  1798  1842 D LOWI-********.z: [LOWIEventReceiver] handleEvent:FROM:FreeWifiScanObserver, TO:LOWI-SERVER, REQ:LOWI_CAPABILITY, IREQ:NA, REQ_ID:4, RESP:NA, INFO:NA
08-27 08:29:57.771  1798  1842 I LOWI-********.z: [LOWIUtils] inPostcardToRequest - FROM: FreeWifiScanObserver, TO:   LOWI-SERVER, REQ:  LOWI_CAPABILITY
08-27 08:29:57.771  1798  1842 D LOWI-********.z: [LOWIController] _process: Request FreeWifiScanObserver_4 received, type(CAPABILITY)
08-27 08:29:57.771  1798  1842 D LOWI-********.z: [LOWIBackgroundScanMgr] isReqForBgScanMgr: CAPABILITY - not handled
08-27 08:29:57.772  1798  1842 D LOWI-********.z: [LOWIScheduler] manageMsg: Request: CAPABILITY -- not handled
08-27 08:29:57.772  1798  1842 I LOWI-********.z: [LOWIController] generateResponse, Get Wifi capabilitiles from controller
08-27 08:29:57.772  1798  1842 I LOWI-********.z: [LOWIController] generateResponse, Get Wigig capabilitiles from controller
08-27 08:29:57.772  1798  1842 D LOWI-********.z: [LOWIController] isWifiEnabled: mNlWifiStatus 0 mWifiStateEnabled 0
08-27 08:29:57.772  1798  1842 I LOWI-********.z: [LOWIController] generateResponse, sending LOWICapabilityResponse status = 0
08-27 08:29:57.772  1798  1842 D LOWI-********.z: [LOWIController] generateResponse: Type (CAPABILITY) ScanStatus (0) request FreeWifiScanObserver_4
08-27 08:29:57.772  1798  1842 D LOWI-********.z: [LOWIEventDispatcher] sendResponse - originator = FreeWifiScanObserver
08-27 08:29:57.772  1798  1842 I LOWI-********.z: [LOWIUtils] responseToOutPostcard - TO: FreeWifiScanObserver, FROM:   LOWI-SERVER, RESP:  LOWI_CAPABILITY
08-27 08:29:57.772  1798  1842 D LOWI-********.z: [LOWIController] processRequestImmediately: Response sent for request(CAPABILITY) FreeWifiScanObserver_4
08-27 08:29:57.772  1798  1842 D LOWI-********.z: [LOWIController] processRequest: Request FreeWifiScanObserver_4 processed immediately
08-27 08:29:57.786  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.786  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.787  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.787  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.788  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.789  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.660   330   330 I [kworke][0x3931a780][08:29:57.811081] wlan: [330:I:QDF] pktlogmod_exit: pkt_log module cleanup
01-02 03:42:57.660   330   330 I [kworke][0x3931a872][08:29:57.811093] wlan: [330:I:QDF] pktlog_detach: detach pktlog resources
08-27 08:29:57.812  1598  1598 I tlog    : type=1400 audit(0.0:152): avc: denied { rename } for name="-00001_kernel_00052_700102_034240.log.ing" dev="dm-6" ino=14394 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:29:57.812  1598  1598 I tlog    : type=1400 audit(0.0:153): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
08-27 08:29:57.812   578   578 W hwservicemanage: type=1400 audit(0.0:154): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:29:57.817  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:29:57.817  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.817  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:29:57.817  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:29:57.817  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.817  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.818  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.818  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.819  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.665   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:42:57.666   578   578 I binder  : send failed reply for transaction 57975 to 1598:1620
01-02 03:42:57.668   578   578 W audit   : audit_lost=23 audit_rate_limit=5 audit_backlog_limit=64
01-02 03:42:57.668   578   578 E audit   : rate limit exceeded
01-02 03:42:57.669   330   330 I [kworke][0x39347df2][08:29:57.820767] wlan: [330:I:OSIF] os_if_spectral_free_skb: Socket buffer is null, msg_type= 0
01-02 03:42:57.673   330   330 I [kworke][0x393583eb][08:29:57.824260] wlan: [330:I:OSIF] os_if_spectral_free_skb: Socket buffer is null, msg_type= 1
01-02 03:42:57.673   330   330 I [kworke][0x393585b4][08:29:57.824283] wlan: [330:I:OSIF] os_if_spectral_free_skb: Socket buffer is null, msg_type= 2
08-27 08:29:57.824  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:29:57.824  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:29:57.824  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:42:57.673   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:42:57.673   578   578 I binder  : send failed reply for transaction 57979 to 1598:1620
08-27 08:29:57.847  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.847  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.847  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.848  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.849  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.849  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.709   330   330 I [kworke][0x39400d02][08:29:57.860221] wlan: [330:I:SPECTRAL] target_if_spectral_detach: spectral detach
01-02 03:42:57.709   330   330 I [kworke][0x39400e67][08:29:57.860239] wlan: [330:I:GREEN_AP] wlan_green_ap_pdev_obj_destroy_notification: Deleting green ap pdev obj, green ap ctx: ffffff9ce5501e68, pdev: ffffff9cbe08a868
01-02 03:42:57.709   330   330 I [kworke][0x39400f00][08:29:57.860247] wlan: [330:I:GREEN_AP] wlan_green_ap_pdev_obj_destroy_notification: green ap deletion successful
01-02 03:42:57.725   330   330 I [kworke][0x3944cad8][08:29:57.876406] wlan: [330:I:IPA] ipa_component_config_free: Free the IPA config memory
01-02 03:42:57.726   330   330 I [kworke][0x3944f216][08:29:57.876929] wlan: [330:I:HIF] hif_ipci_disable_bus: X
08-27 08:29:57.877  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.878  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.878  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.879  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.879  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.879  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.883   904   960 E minksocket: MinkIPC_QRTR_Service: client with node d port 22 went down
08-27 08:29:57.887  1749  2198 I cnss-daemon: wlan_qmi_err_cb: WLPS service disconnect, called with error -2 for client 0x3K
08-27 08:29:57.889  1749  4682 I cnss-daemon: wlan_service_request: Start the pthread
08-27 08:29:57.890   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 413e went down
08-27 08:29:57.892   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4184 went down
08-27 08:29:57.908  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.908  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.909  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.909  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.910  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.910  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.938  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.938  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.939  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.940  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.940  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.940  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.968  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.968  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.969  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.970  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.970  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.971  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:57.836   330   330 I [kworke][0x39655302][08:29:57.987448] wlan: [330:I:HIF] hif_latency_detect_timer_deinit: deinit timer
08-27 08:29:57.999  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.999  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:57.999  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.000  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.001  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.001  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.029  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.029  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.029  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.030  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.031  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.031  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.049  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:29:58.049  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:29:58.050  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:29:58.050   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:29:58.050   731   747 I mpu_uart: [TIME-:20]:create
08-27 08:29:58.059  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.059  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.060  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.061  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.061  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.061  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.090  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.090  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.090  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.091  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.091  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.092  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.120  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.120  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.120  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.121  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.122  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.122  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.150  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.150  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.151  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.152  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.152  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.152  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.181  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.181  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.181  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.182  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.182  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.183  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.211  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.211  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.211  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.212  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.213  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.213  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.242  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.242  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.242  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.243  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.243  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.243  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.272  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.272  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.272  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.273  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.273  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.274  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.302  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.302  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.303  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.304  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.304  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.304  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.333  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.333  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.333  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.334  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.334  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.334  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.363  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.363  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.363  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.364  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.365  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.365  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.394  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.394  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.394  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.395  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.395  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.395  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.405   904   930 E ssgtzd  : Modemcomm: Attempt 0 : Init Instance failed with -3
08-27 08:29:58.424  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.424  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.424  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.425  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.425  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.425  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.454  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.454  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.454  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.456  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.456  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.456  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.485  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.485  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.485  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.486  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.486  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.487  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.507  3430  4079 D BufferPoolAccessor2.0: bufferpool2 0xb4000070f2f5cc78 : 0(0 size) total buffers - 0(0 size) used buffers - 1/7 (recycle/alloc) - 6/64 (fetch/transfer)
08-27 08:29:58.507  3430  4079 D BufferPoolAccessor2.0: evictor expired: 1, evicted: 1
08-27 08:29:58.515  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.515  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.515  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.516  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.516  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.517  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.545  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.545  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.545  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.546  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.546  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.547  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.559   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:29:58.576  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.576  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.576  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.577  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.577  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.578  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.578   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:29:58.579   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:29:58.579   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:29:58.606  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.606  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.606  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.607  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.608  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.608  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.636  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.637  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.637  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.637  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.638  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.638  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.667  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.667  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.667  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.668  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.668  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.668  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.697  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.697  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.698  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.698  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.698  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.699  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.728  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.728  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.728  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.728  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.729  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.729  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.758  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.758  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.758  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.758  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.759  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.759  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.788  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.789  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.789  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.789  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.789  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.790  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.819  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.819  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.819  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.819  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.819  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.820  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.849  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.849  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.850  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.850  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.850  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.850  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.880  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.880  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.880  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.880  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.880  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.880  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.910  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.910  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.911  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.911  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.911  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.911  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.941  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.941  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.941  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.941  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.941  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.941  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.971  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.971  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.971  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.971  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.971  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:58.971  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.001  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.002  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.002  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.002  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.002  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.002  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:58.850   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.859   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.868   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.878   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.032  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.032  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.032  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.032  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.032  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.032  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:58.887   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.896   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.905   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.062  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.062  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.062  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.063  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.063  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.063  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:58.914   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.923   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.932   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.092  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.093  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.093  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.093  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.093  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.093  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:58.941   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.950   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.959   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.968   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.123  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.123  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.124  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.124  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.125  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:58.977   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.132  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:58.986   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:58.996   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.153  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.005   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.163  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.163  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.163  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.163  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.163  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.014   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.174  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.023   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.183  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.032   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.193  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.193  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.193  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.194  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.041   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.195  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.050   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.059   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.217  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.068   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.223  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.224  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.225  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.225  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.225  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.226  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.077   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.086   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.247  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.095   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.254  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.255  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.255  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.256  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.256  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.104   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.257  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.113   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.122   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.278  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.284  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.131   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.286  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.286  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.287  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.288  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.290  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.140   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.149   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.308  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.158   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.315  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.316  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.316  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.317  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.319  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.167   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.176   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.185   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.338  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.341  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.345  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.347  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.347  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.347  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.349  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.194   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.203   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.212   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.369  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.221   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.375  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.377  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.377  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.377  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.379  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.230   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.391  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.239   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.399  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.249   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.406  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.258   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.407   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4185 went down
08-27 08:29:59.407  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.407  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.408  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.410  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.410   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4186 went down
08-27 08:29:59.412   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4187 went down
01-02 03:42:59.267   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.276   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.429  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.436  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.285   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.438  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.438  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.438  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.440  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.442  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.294   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.303   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.460  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.461  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.312   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.466  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.468  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.468  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.469  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.470  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.321   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.330   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.492  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.492  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.339   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.497  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.499  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.499  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.499  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.500  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.348   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.357   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.510  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.366   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.522  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.527  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.529  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.529  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.375   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.530  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.531  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.384   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.543  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.393   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.552  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.402   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.557  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.559   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:29:59.560  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.560  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.561  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.561  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.561  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.411   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.420   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.577  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.579   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:29:59.579   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:29:59.579   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:42:59.429   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.583  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.588  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.590  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.591  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.592  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.593  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.438   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.594  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.447   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.456   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.611  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.613  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.619  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.620  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.622  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.466   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.623  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.623  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.475   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.628  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.484   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.493   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.643  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.644  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.650  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.651  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.652  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.653  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.502   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.656  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.662  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.511   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.520   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.674  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.679  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.680  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.681  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.529   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.683  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.684  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.686  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.538   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.547   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.704  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.556   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.710  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.711  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.712  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.714  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.565   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.714  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.717  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.575   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.729  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.735  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.584   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.741  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.742  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.744  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.745  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.593   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.747  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.602   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.763  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.611   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.765  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.771  1053  1053 D vendor.qti.vibrator: Vibrator on for timeoutMs: 20
08-27 08:29:59.771  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.620   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.621   456   456 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
01-02 03:42:59.629   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.629   456   456 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
08-27 08:29:59.772  1053  4689 D vendor.qti.vibrator: Starting on on another thread
08-27 08:29:59.772  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.774  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.775  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.777  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.780  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.638   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.638   456   456 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
08-27 08:29:59.792  1053  4689 D vendor.qti.vibrator: Notifying on complete
08-27 08:29:59.793  1053  1053 D vendor.qti.vibrator: QTI Vibrator off
08-27 08:29:59.795  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.647   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.802  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.802  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.805  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.805  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.807  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.656   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.665   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.815  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.826  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.674   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.832  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.832  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.835  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.835  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.683   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.838   905  3532 D audio_hw_primary: adev_get_parameters:vr_audio_mode_on
08-27 08:29:59.839  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.692   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.847  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.701   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.854  1118  1118 D AF::TrackHandle: OpPlayAudio: track:55 usage:13 not muted
08-27 08:29:59.856  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.862  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.863  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.864  1057  2089 I AudioTrack: createTrack_l(0): AUDIO_OUTPUT_FLAG_FAST successful; frameCount 0 -> 4512
01-02 03:42:59.710   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.865  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.866  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.868  2200  2218 W ndroid.systemui: Cleared Reference was only reachable from finalizer (only reported once)
08-27 08:29:59.869  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.719   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.728   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.882  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.884  2200  2218 I ndroid.systemui: Background concurrent copying GC freed 425949(20MB) AllocSpace objects, 14(336KB) LOS objects, 75% free, 11MB/47MB, paused 61us,60us total 110.958ms
08-27 08:29:59.887  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.737   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.893  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.893  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.896  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.896  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.746   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.900  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.907   905  1024 E FMQ     : grantorIdx must be less than 3
01-02 03:42:59.756   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.908   905  1024 E FMQ     : grantorIdx must be less than 3
08-27 08:29:59.915  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.765   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.918  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.919   905  4692 D audio_hw_primary: start_output_stream: enter: stream(0xee0c64c0)usecase(1: low-latency-playback) devices(0x2) is_haptic_usecase(0)
08-27 08:29:59.923  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.923  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.925  1057  1363 D CompatibilityChangeReporter: Compat change id reported: 168419799; UID 10046; state: DISABLED
08-27 08:29:59.925  1057  1363 D CompatibilityChangeReporter: Compat change id reported: 273564678; UID 10046; state: DISABLED
08-27 08:29:59.925  1057  1363 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:59.925  1057  1363 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:59.926  1057  1363 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:59.926  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.927  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.774   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.929  4483  4483 D CompatibilityChangeReporter: Compat change id reported: 194532703; UID 10046; state: ENABLED
01-02 03:42:59.783   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.930  1057  3765 D CompatibilityChangeReporter: Compat change id reported: 194532703; UID 10046; state: ENABLED
08-27 08:29:59.930  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.931   578   578 I hwservicemanager: getTransport: Cannot find entry android.hardware.power@1.2::IPower/default in either framework or device VINTF manifest.
08-27 08:29:59.931   905  4692 E audio_hw_primary: Unable to get Power service
08-27 08:29:59.936  1057  3765 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:29:59.936  1057  3765 D CompatibilityInfo: applicationDensity - 160
08-27 08:29:59.936  1057  3765 D CompatibilityInfo: applicationScale - 1.0
08-27 08:29:59.937   905  4692 D audio_hw_primary: select_devices for use case (low-latency-playback)
01-02 03:42:59.792   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.938   905  4692 D audio_hw_primary: select_devices: changing use case low-latency-playback output device from(0: , acdb -1) to (2: speaker, acdb 15)
08-27 08:29:59.938   905  4692 I msm8974_platform: platform_check_and_set_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-27 08:29:59.938   905  4692 I msm8974_platform: platform_check_and_set_codec_backend_cfg: new_snd_devices[0] is 2
08-27 08:29:59.938   905  4692 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-27 08:29:59.948  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.801   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.950  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.954  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.954  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.956   905  4692 D msm8974_platform: platform_check_codec_backend_cfg:becf: updated afe: bitwidth 16, samplerate 48000 channels 2,backend_idx 0 usecase = 1 device (speaker)
01-02 03:42:59.810   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:29:59.956   905  4692 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: Codec selected backend: 0 updated bit width: 16 and sample rate: 48000
08-27 08:29:59.956   905  4692 D audio_hw_primary: check_usecases_codec_backend:becf: force routing 0
08-27 08:29:59.956   905  4692 D audio_hw_primary: check_usecases_codec_backend:becf: (93) check_usecases curr device: speaker, usecase device: backends match 0
08-27 08:29:59.956   905  4692 D audio_hw_primary: check_usecases_codec_backend:becf: check_usecases num.of Usecases to switch 0
08-27 08:29:59.956   905  4692 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 08:29:59.956   905  4692 D audio_hw_primary: enable_snd_device: snd_device(2: speaker)
08-27 08:29:59.956   905  4692 D msm8974_platform: platform_get_island_cfg_on_device:island cfg status on snd_device = (speaker 0)
08-27 08:29:59.956   905  4692 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 1, with Raise=0
08-27 08:29:59.957  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.957  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.960   905  4692 D audio_route: Apply path: speaker
08-27 08:29:59.960   905  4692 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 3, with Raise=0
08-27 08:29:59.960   905  4692 D audio_hw_utils: audio_extn_utils_send_app_type_cfg: usecase->out_snd_device speaker
08-27 08:29:59.960  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.963   905  4692 I audio_hw_utils: send_app_type_cfg_for_device PLAYBACK app_type 69937, acdb_dev_id 15, sample_rate 48000, snd_device_be_idx 39
08-27 08:29:59.965  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.819   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.828   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.829  4692  4692 I [Awinic][1-0034]aw882xx_startup: playback enter
01-02 03:42:59.829  4692  4692 E aw_cali_get_read_cali_re: channel:0 open /mnt/vendor/persist/factory/audio/aw_cali.bin failed!
08-27 08:29:59.966   905  4692 D ACDB-LOADER: ACDB -> send_audio_cal, acdb_id = 15, path = 0, app id = 69937, sample rate = 48000, use_case = 0,buffer_idx_w_path =0, afe_sample_rate = 48000, cal_mode = 1, offset_index = 0
08-27 08:29:59.966   905  4692 D ACDB-LOADER: ACDB -> send_asm_topology
08-27 08:29:59.966   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TOPOLOGY_ID
08-27 08:29:59.971   905  4692 D ACDB-LOADER: ACDB -> send_adm_topology
08-27 08:29:59.971   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TOPOLOGY_ID
08-27 08:29:59.972   905  4692 D ACDB-LOADER: ACDB -> send_audtable
08-27 08:29:59.972   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE_SIZE
08-27 08:29:59.972   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE
08-27 08:29:59.972   905  4692 D ACDB-LOADER: ACDB -> AUDIO_SET_AUDPROC_CAL cal_type[11] acdb_id[15] app_type[69937]
08-27 08:29:59.972   905  4692 D ACDB-LOADER: ACDB -> send_audvoltable
08-27 08:29:59.972   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE_SIZE
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE, vol index 0
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> AUDIO_SET_VOL_CAL cal type = 12
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE_SIZE
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> send_audstrmtable
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> audstrm_cal->cal_type.cal_data.cal_size = 20
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> send_afe_topology
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_TOPOLOGY_ID
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> GET_AFE_TOPOLOGY_ID for adcd_id 15, Topology Id 112fc
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> send_afe_cal
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE
08-27 08:29:59.973   905  4692 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 08:29:59.973   905  4692 D ACDB-LOADER: Error: ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE Returned = -19
08-27 08:29:59.973   905  4692 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE
08-27 08:29:59.973   905  4692 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 08:29:59.973   905  4692 D ACDB-LOADER: Error: ACDB AFE returned = -19
08-27 08:29:59.974   905  4692 D ACDB-LOADER: ACDB -> AUDIO_SET_AFE_CAL cal_type[16] acdb_id[15]
08-27 08:29:59.974   905  4692 D ACDB-LOADER: ACDB -> send_hw_delay : acdb_id = 15 path = 0
08-27 08:29:59.974   905  4692 D ACDB-LOADER: ACDB -> ACDB_AVSYNC_INFO: ACDB_CMD_GET_DEVICE_PROPERTY
08-27 08:29:59.974   905  4692 D audio_hw_primary: enable_audio_route: apply mixer and update path: low-latency-playback
08-27 08:29:59.974   905  4692 D audio_route: Apply path: low-latency-playback
08-27 08:29:59.977   905  4692 D audio_hw_primary: select_devices: done
08-27 08:29:59.977   905  4692 D msm8974_platform: platform_set_channel_map mixer_ctl_name:Playback Channel Map9
08-27 08:29:59.978   905  4692 D msm8974_platform: platform_set_channel_map: set mapping(1 2 0 0 0 0 0 0) for channel:2
08-27 08:29:59.982  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.982  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:29:59.984  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.984  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.991  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.991  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:29:59.991  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.837   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.840  4692  4692 I [Awinic][1-0034]aw882xx_dev_init_cali_re: read nvram cali failed, use default Re
01-02 03:42:59.846   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.000  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.855   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.012  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.015  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.014  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.864   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.021  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.021  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.021  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.873   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.033  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.878  4692  4692 E msm_adsp_init_mixer_ctl_adm_pp_event_queue: failed to get kctl.
01-02 03:42:59.882   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.889  4692  4692 I afe_get_cal_topology_id: port_id = 0x1006 acdb_id = 15 topology_id = 0x112fc cal_type_index=8 ret=0
08-27 08:30:00.045  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.889  4692  4692 E send_afe_cal_type: No cal sent for cal_index 0, port_id = 0x1006! ret -22
01-02 03:42:59.891   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.045  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.049  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.052  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.052  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.052  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.900   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.901  4692  4692 I afe_send_hw_delay: port_id 0x1006 rate 48000 delay_usec 474 status 0
01-02 03:42:59.906  4692  4692 I [Awinic][1-0034]aw882xx_mute: mute state=0
01-02 03:42:59.906  4692  4692 I [Awinic][1-0034]aw882xx_spin_set_record_val: do nothing
01-02 03:42:59.906  4692  4692 I [Awinic][1-0034]aw882xx_spin_set_record_val: set record spin val done
01-02 03:42:59.907   330   330 I [Awinic][1-0034]aw882xx_startup_work: enter
01-02 03:42:59.907   330   330 I [Awinic][1-0034]aw882xx_start_pa: enter
01-02 03:42:59.907   330   330 I [Awinic][1-0034]aw882xx_dev_reg_update: done
01-02 03:42:59.908   330   330 I [Awinic][1-0034]aw_dev_pwd: done
01-02 03:42:59.909  4692  4692 E q6asm_find_cal_by_buf_number: Can't find ASM Cal for cal_index 2 app_type 69937 buffer_number 8
01-02 03:42:59.909   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.921   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.922   330   330 I [Awinic][1-0034]aw_dev_mode1_pll_check: done
08-27 08:30:00.074  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.923   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.923   330   330 I [Awinic][1-0034]aw_dev_amppd: done
01-02 03:42:59.926   330   330 I [Awinic][1-0034]aw_dev_sysst_check: done
01-02 03:42:59.927   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.929   330   330 I [Awinic][1-0034]aw_pid_2113_reg_force_set: needn't set reg value
01-02 03:42:59.931   330   330 I [Awinic][1-0034]aw_dev_uls_hmute: done
08-27 08:30:00.075  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.075  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.079   905  4692 D audio_hw_primary: start_output_stream: exit
08-27 08:30:00.081  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.082  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.082  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.082  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.936   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.945   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.951   330   330 I [Awinic][1-0034]aw_dev_mute: done
01-02 03:42:59.952   330   330 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-02 03:42:59.952   330   330 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-02 03:42:59.952   330   330 I [Awinic][1-0034]aw882xx_monitor_start: enter
01-02 03:42:59.952   330   330 I [Awinic][1-0034]aw882xx_device_start: done
01-02 03:42:59.952   330   330 I [Awinic][1-0034]aw882xx_start_pa: start success
01-02 03:42:59.953   330   330 I [Awinic][1-0034]aw_monitor_get_voltage: chip voltage is 3957
01-02 03:42:59.953   330   330 I [Awinic][1-0034]aw_monitor_get_temperature: reg val is 0x001c
01-02 03:42:59.953   330   330 I [Awinic][1-0034]aw_monitor_get_temperature: chip temperature = 28
01-02 03:42:59.954   330   330 I [Awinic][1-0034]aw_monitor_set_ipeak: set reg val = 0xa96c, ipeak = 0xa000
01-02 03:42:59.954   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.106  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.106  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.107  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.111  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.112  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.956   330   330 I [Awinic][1-0034]aw_monitor_set_gain: set reg val = 0x15, gain = 0x4
01-02 03:42:59.956   330   330 E [Awinic]aw_check_dsp_ready: rx topo id is 0x0
01-02 03:42:59.963   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.114  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.115  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.124  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:42:59.972   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:42:59.981   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.136  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.136  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.141  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.143  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.990   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.145  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.145  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:42:59.999   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.008   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.167  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.167  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.018   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.171  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.174  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.175  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.175  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.176  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.027   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.036   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.045   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.197  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.197  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.202  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.204  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.054   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.206  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.207  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.208  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.063   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.072   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.227  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.228  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.232  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.081   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.234  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.236  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.238  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.090   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.099   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.258  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.258  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.259  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.108   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.262  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.266  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.266  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.268  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.117   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.126   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.288  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.288  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.135   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.293  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.296  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.296  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.144   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.298  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.153   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.309  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.162   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.318  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.318  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.323  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.326  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.327  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.171   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.329  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.180   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.189   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.349  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.349  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.198   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.353  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.357  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.357  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.359  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.207   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.360  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.216   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.225   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.379  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.379  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.383  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.387  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.387  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.389  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.242   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.251   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.409  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.410  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.411  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.411   904   930 E ssgtzd  : Modemcomm: Attempt 1 : Init Instance failed with -3
08-27 08:30:00.414  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.260   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.417  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.418  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.420  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.269   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.278   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.440  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.441  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.287   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.444  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.448  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.448  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.296   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.450  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.305   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.461  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.314   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.470  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.471  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.474  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.478  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.323   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.478  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.479  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.480  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.332   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.342   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.498   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:30:00.498   731   731 I mpu_uart: [TIME-:19]:delete
08-27 08:30:00.499   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x32, 0x38, 0x30, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x23, ]
08-27 08:30:00.499   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20280,current_state=0, ota_state = 1
08-27 08:30:00.499   731   745 V mpu_uart:  >> log: 
08-27 08:30:00.500  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.501  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.504  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.351   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.508  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.508  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.360   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.511  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.512  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.515   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:30:00.515   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x32, 0x39, 0x20, 0x6d, 0x76, 0xa, 0x77, ]
08-27 08:30:00.515   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12329 mv
08-27 08:30:00.515   731   745 V mpu_uart:  >> log: 
08-27 08:30:00.519   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:30:00.519   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
01-02 03:43:00.369   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.378   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.528   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:30:00.528   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:30:00.528   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:30:00.528   731   745 V mpu_uart:  >> log: 
08-27 08:30:00.528   731   747 I mpu_uart: [TIME-:20]:delete
08-27 08:30:00.529  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:30:00.529  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:30:00.529  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.529   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:00.529   731   747 I mpu_uart: [TIME-:21]:create
08-27 08:30:00.531  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.531  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.535  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.538   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:00.538   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:30:00.538   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:00.539  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.539  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.539  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:30:00.540  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:30:00.540  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
01-02 03:43:00.387   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.542  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.548   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:30:00.548   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:30:00.548   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:30:00.548   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x1b, 0x80, ]
01-02 03:43:00.396   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.549   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:00.549   731   747 I mpu_uart: [TIME-:21]:delete
08-27 08:30:00.550  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x1B 
08-27 08:30:00.550  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:30:00.551  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:30:00.551  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12315
08-27 08:30:00.551  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12315
01-02 03:43:00.405   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.559   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:30:00.559   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 08:30:00.559   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
01-02 03:43:00.414   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.561  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.562  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.563  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.564  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 08:30:00.564  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:30:00.564  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:30:00.564  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 08:30:00.564  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
08-27 08:30:00.564  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 08:30:00.567  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.569  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.571  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:30:00.571  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.572  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.423   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.579  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.432   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.591  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.592  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.441   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.597  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.598  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.599  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.450   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.602  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.602  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.459   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.613  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.468   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.622  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.622  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.628  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.630  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.477   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.631  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.632  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.633  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.486   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.495   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.652  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.652  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.504   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.659  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.659   657  2228 I keystore2: keystore2::watchdog: Watchdog thread idle -> terminating. Have a great day.
08-27 08:30:00.660  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.662  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.663  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.664  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.513   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.522   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.681  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.682  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.683  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.531   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.689  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.691  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.693  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.693  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.540   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.698  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.549   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.558   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.713  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.713  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.715  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.567   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.720  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.721  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.723  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.723  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.577   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.732  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.585   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.743  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.744  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.748  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.594   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.750  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.751  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.753  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.754  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.603   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.758  1057  1363 D CompatibilityChangeReporter: Compat change id reported: 135634846; UID 10127; state: DISABLED
08-27 08:30:00.758  1057  1363 D CompatibilityChangeReporter: Compat change id reported: 177438394; UID 10127; state: DISABLED
08-27 08:30:00.759  1057  1363 D CompatibilityChangeReporter: Compat change id reported: 135772972; UID 10127; state: DISABLED
08-27 08:30:00.759  1057  1363 D CompatibilityChangeReporter: Compat change id reported: 135754954; UID 10127; state: ENABLED
08-27 08:30:00.760  1057  1364 D CompatibilityChangeReporter: Compat change id reported: 143937733; UID 10127; state: ENABLED
08-27 08:30:00.766  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.774  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.775  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.612   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.621   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.781  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.631   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.783  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.783  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.784  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.784  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.786   761   761 D Zygote  : Forked child process 4697
08-27 08:30:00.786  1057  1364 I ActivityManager: Start proc 4697:com.qti.ltebc/u0a127 for broadcast {com.qti.ltebc/com.qualcomm.ltebc.LTEBroadcastAlarmReceiver}
01-02 03:43:00.640   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.649   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.799  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.804  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.805  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.804  4697  4697 W com.qti.ltebc: type=1400 audit(0.0:157): avc: denied { create } for dev="anon_inodefs" ino=102887 scontext=u:r:vendor_embmssl_app:s0:c127,c256,c512,c768 tcontext=u:object_r:vendor_embmssl_app:s0:c127,c256,c512,c768 tclass=anon_inode permissive=0 app=com.qti.ltebc
08-27 08:30:00.811  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.658   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.814  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.815  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.815  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.816  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.817  1877  1885 I adbd    : jdwp connection from 4697
08-27 08:30:00.821  1057  3765 D CompatibilityChangeReporter: Compat change id reported: 168419799; UID 10127; state: DISABLED
08-27 08:30:00.821  1057  3765 D CompatibilityChangeReporter: Compat change id reported: 273564678; UID 10127; state: DISABLED
08-27 08:30:00.821  1057  3765 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:30:00.821  1057  3765 D CompatibilityInfo: applicationDensity - 160
08-27 08:30:00.821  1057  3765 D CompatibilityInfo: applicationScale - 1.0
08-27 08:30:00.822  1057  3765 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:30:00.822  1057  3765 D CompatibilityInfo: applicationDensity - 160
08-27 08:30:00.822  1057  3765 D CompatibilityInfo: applicationScale - 1.0
01-02 03:43:00.667   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.676   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.833  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.835  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.836  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.685   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.841  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.843  4697  4697 D CompatibilityChangeReporter: Compat change id reported: 171979766; UID 10127; state: ENABLED
01-02 03:43:00.694   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.846  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.850  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.851  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.851  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.703   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.712   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.866  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.866  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.867  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.872  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.721   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.876  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.880  3606  3662 W ndroid.keychain: Suspending all threads took: 10.310ms
08-27 08:30:00.882  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.730   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.882  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.884  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.739   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.896  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.896  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.899  4697  4697 I Perf    : Connecting to perf service.
01-02 03:43:00.748   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.901  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.903  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.907  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.757   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.910  4697  4697 V GraphicsEnvironment: ANGLE Developer option for 'com.qti.ltebc' set to: 'default'
08-27 08:30:00.912  4697  4697 V GraphicsEnvironment: ANGLE GameManagerService for com.qti.ltebc: false
08-27 08:30:00.912  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.913  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.918  4697  4697 D NetworkSecurityConfig: Using Network Security Config from resource network_security_config debugBuild: false
08-27 08:30:00.918  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.919  4697  4697 D NetworkSecurityConfig: Using Network Security Config from resource network_security_config debugBuild: false
01-02 03:43:00.766   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.927  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.927  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.775   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.930  4697  4697 I LTE Application: LTEApplicationCreate
08-27 08:30:00.931  4697  4697 I LTE Application: applicationContext : com.qualcomm.ltebc.LTEQTIApplication@3a5721a
08-27 08:30:00.933  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.935  4697  4697 I LTE QMI Link: Creating QMI Link
08-27 08:30:00.935  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.936  4697  4697 D LTE Application: packageName = com.qti.ltebc versionCode = 5402021 versionName = MSDC_LA_5.4.02.02.1
08-27 08:30:00.937  4697  4697 I LTE Application: LTE Broadcast Library Release Version = MSDC_LA_5.4.02.02.1
08-27 08:30:00.937  4697  4697 D LTE Application: IntentSender : Last intent Status NOT_SENT forcedBroadcast flag = true
08-27 08:30:00.937  4697  4697 D LTE Application: sendBroadcastIntent IntentAction : Sending com.qualcomm.intent.EMBMS_STATUS ACTIVE = false
08-27 08:30:00.937  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.784   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.942  4697  4697 W SOCKET  : ConnectionManager constructor has been initialised
08-27 08:30:00.942  4697  4697 E System  : Ignoring attempt to set property "java.net.preferIPv6Addresses" to value "false".
08-27 08:30:00.943  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.943  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.793   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.945  4697  4697 D LTE Application: Android API Version 33
08-27 08:30:00.946  4697  4697 I LTE Application: Package Name : com.qti.ltebc
08-27 08:30:00.949  4697  4697 I LTE Application: app is not whitelisted
01-02 03:43:00.802   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.811   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.952  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.952  4697  4697 I LTEBroadcastAlarmReceiver: onReceive
08-27 08:30:00.952  4697  4697 I LTEBroadcastAlarmReceiver: ACTION_ALARM_WAKEUP_BOOT
08-27 08:30:00.952  4697  4697 I LTE Application: setStartupReason - set to STARTUP_REASON_BOOT
08-27 08:30:00.952  4697  4697 I LTE Application: startRootServiceASync false
08-27 08:30:00.955  4697  4697 I LTEBroadcastAlarmReceiver: onReceive exit
08-27 08:30:00.957  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.958  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.960  4697  4715 I LTE Application: startRootServiceSync false
08-27 08:30:00.960  4697  4715 I LTE Application: startRootServiceSync initState LTEBC_MSP_NOT_INITIALIZED
08-27 08:30:00.960  4697  4715 I LTE Application: setWakeupMode -  mode :WAKEUP_MODE_BG
08-27 08:30:00.960  4697  4715 I LTE Application: startRootServiceSync(): Started from Alarm WAKEUP_MODE_BG
08-27 08:30:00.961  4697  4715 I LTE Application: Starting LTEBC Root Service : launcherIntent = Intent { cmp=com.qti.ltebc/com.qualcomm.ltebc.LTERootService }
08-27 08:30:00.964  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.968  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.969  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.969  1057  3765 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:30:00.969  1057  3765 D CompatibilityInfo: applicationDensity - 160
08-27 08:30:00.969  1057  3765 D CompatibilityInfo: applicationScale - 1.0
08-27 08:30:00.971  4697  4715 I LTE Application: shutdownLockOn() Current sKeepAlive: 0, After Incrementing : 1
08-27 08:30:00.971  4697  4715 I LTE Application: cancelShutDownTimer mspShutDownTimer is NULL 
08-27 08:30:00.972  4697  4715 V LTE Application: initStateChanged - LTEBC_PERMISSIONS_PENDING
08-27 08:30:00.972  4697  4714 I LTEBC Init Manager: handleStartInitialization
01-02 03:43:00.820   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.829   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.973  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.973  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.974  4697  4714 I LTE Root Service: [EVENT] row getManifestDeniedPermissionsList invoked
08-27 08:30:00.974  4697  4714 I LTEBC Init Manager: Handled state : 1
08-27 08:30:00.974  4697  4714 I LTEBC Init Manager: handlePermissionResult
08-27 08:30:00.974  4697  4714 V LTE Application: initStateChanged - LTEBC_MSP_INIT_PENDING
08-27 08:30:00.974  4697  4697 I LTE Root Service: onCreate()
08-27 08:30:00.974  4697  4714 I LTE Application: handleInitConfigandConnectEmbmsServiceTask : doInBackground
08-27 08:30:00.975  4697  4697 I LTE Root Service: onStartCommand()
08-27 08:30:00.986  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:00.988  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.988  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.838   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:00.994  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:00.998  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.847   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.003  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.003  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.004  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.856   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.866   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.019  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.020  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.020  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.025  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.875   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.029  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.034  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.034  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.884   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.037  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.041  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:30:01.042  2543  2743 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:30:01.044  3511  3822 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:30:01.045  3511  4058 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:30:01.045  3511  4058 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:30:01.045  3511  4058 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@e8e7404
08-27 08:30:01.045  3511  4058 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:30:01.045  3511  4058 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:30:01.046  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:30:01.046  2543  2743 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:30:01.046  2543  2743 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:30:01.047  2543  2743 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:30:01.047   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:30:01.047   731   747 I mpu_uart: [TIME-:22]:create
08-27 08:30:01.047  1057  3765 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:30:01.047  1057  3765 E ActivityManager: java.lang.Throwable
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:30:01.047  1057  3765 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
01-02 03:43:00.893   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.050  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.050  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.902   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.054  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.055  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.057   731   744 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:30:01.057   731   744 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:30:01.059  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.060   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:01.060   731   747 I mpu_uart: [TIME-:22]:delete
08-27 08:30:01.060   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:30:01.061   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:30:01.062  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
01-02 03:43:00.910   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.063  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:30:01.063  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:30:01.063  2543  2743 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:30:01.063   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:01.063   731   747 I mpu_uart: [TIME-:23]:create
08-27 08:30:01.064  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.064  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.071  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.919   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.080  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.081   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:01.081   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:01.081  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.928   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.086  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.087  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:00.937   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.090  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.094  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.095  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.946   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.955   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.110  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.112  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.116  1057  1057 W NotificationHistory: Attempted to add notif for locked/gone/disabled user 0
08-27 08:30:01.116  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.964   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.121  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.122  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.125  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.125  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.973   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:00.982   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.138  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.141  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:00.991   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.143  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.146  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.152  1057  1209 D CompatibilityChangeReporter: Compat change id reported: 149924527; UID 10127; state: ENABLED
08-27 08:30:01.152  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.152  1057  1209 D CompatibilityChangeReporter: Compat change id reported: 132649864; UID 10127; state: DISABLED
01-02 03:43:01.000   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.155  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.156  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.156  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.010   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.167  4697  4714 I Ltebc   :  ConfigurationManager () : LTEApplication.internalDataPath /data/user/0/com.qti.ltebc/files
08-27 08:30:01.168  4697  4714 D LTE_CONFIG: getVendorPath /vendor
08-27 08:30:01.172  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.172  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.174  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.019   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.177  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.028   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.182  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.186  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.186  2200  2458 D LocalImageResolver: Couldn't use ImageDecoder for drawable, falling back to non-resized load.
08-27 08:30:01.187  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.037   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.189  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.046   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.195  2200  2458 D LocalImageResolver: Couldn't use ImageDecoder for drawable, falling back to non-resized load.
08-27 08:30:01.196  4697  4714 I  Ltebc  :  Provisioning directories:[/vendor/embms/qcom, /vendor/embms/oem, /vendor/embms/qti, /vendor/embms/qcom/global, /vendor/embms/oem/global, /vendor/embms/qti/global, /system/embms/qcom, /system/embms/oem, /system/embms/qti, /system/embms/qcom/global, /system/embms/oem/global, /system/embms/qti/global, /vendor/app/embms/oem, /vendor/app/embms/oem/global, /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom, /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global]
08-27 08:30:01.197  4697  4714 I  Ltebc  :  Provisioning directories Assets:[/carrierConfig/embms]
08-27 08:30:01.197  4697  4714 D LTE_CONFIG: configurable provisioningLocation: [/vendor/embms/qcom, /vendor/embms/oem, /vendor/embms/qti, /vendor/embms/qcom/global, /vendor/embms/oem/global, /vendor/embms/qti/global, /system/embms/qcom, /system/embms/oem, /system/embms/qti, /system/embms/qcom/global, /system/embms/oem/global, /system/embms/qti/global, /vendor/app/embms/oem, /vendor/app/embms/oem/global, /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom, /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global]
08-27 08:30:01.197  4697  4714 D LTE_CONFIG: copyFilesFromAssets
08-27 08:30:01.197  4697  4714 D LTE_CONFIG: copyFilesFromAssets : location = carrierConfig/embms
08-27 08:30:01.200  4697  4714 D LTE_CONFIG: copyFilesFromAssets : list of files in carrierConfig/embms = []
08-27 08:30:01.200  4697  4714 D LTE_CONFIG: copyFilesFromAssets : list of files in carrierConfig/embms is empty
08-27 08:30:01.200  4697  4714 D LTE_CONFIG: files copy operation from Assets folder failed
08-27 08:30:01.200  4697  4714 D LTE_CONFIG:  oemProvisioningDirs : [/vendor/embms/qcom, /vendor/embms/oem, /vendor/embms/qti, /vendor/embms/qcom/global, /vendor/embms/oem/global, /vendor/embms/qti/global, /system/embms/qcom, /system/embms/oem, /system/embms/qti, /system/embms/qcom/global, /system/embms/oem/global, /system/embms/qti/global, /vendor/app/embms/oem, /vendor/app/embms/oem/global, /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom, /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global]
08-27 08:30:01.200  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/embms/qcom
08-27 08:30:01.202  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.055   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.203  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/embms/qcom
08-27 08:30:01.204  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.204  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/embms/oem
08-27 08:30:01.204  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/embms/oem
08-27 08:30:01.204  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.204  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/embms/qti
08-27 08:30:01.205  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/embms/qti
08-27 08:30:01.205  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.205  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.205  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/embms/qcom/global
08-27 08:30:01.205  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/embms/qcom/global
08-27 08:30:01.205  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.206  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/embms/oem/global
08-27 08:30:01.206  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/embms/oem/global
08-27 08:30:01.206  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.206  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.206  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/embms/qti/global
08-27 08:30:01.206  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/embms/qti/global
08-27 08:30:01.206  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.206  4697  4714 D LTE_CONFIG:  Checking dir: /system/embms/qcom
08-27 08:30:01.207  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /system/embms/qcom
08-27 08:30:01.207  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.208  4697  4714 D LTE_CONFIG:  Checking dir: /system/embms/oem
08-27 08:30:01.208  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.208  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /system/embms/oem
08-27 08:30:01.208  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.208  4697  4714 D LTE_CONFIG:  Checking dir: /system/embms/qti
08-27 08:30:01.208  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /system/embms/qti
08-27 08:30:01.208  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.209  4697  4714 D LTE_CONFIG:  Checking dir: /system/embms/qcom/global
08-27 08:30:01.209  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /system/embms/qcom/global
08-27 08:30:01.209  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.209  4697  4714 D LTE_CONFIG:  Checking dir: /system/embms/oem/global
08-27 08:30:01.209  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /system/embms/oem/global
08-27 08:30:01.209  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.209  4697  4714 D LTE_CONFIG:  Checking dir: /system/embms/qti/global
08-27 08:30:01.209  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /system/embms/qti/global
08-27 08:30:01.209  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.209  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/app/embms/oem
08-27 08:30:01.210  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/app/embms/oem
08-27 08:30:01.210  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.210  4697  4714 D LTE_CONFIG:  Checking dir: /vendor/app/embms/oem/global
08-27 08:30:01.210  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /vendor/app/embms/oem/global
08-27 08:30:01.210  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.210  4697  4714 D LTE_CONFIG:  Checking dir: /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom
08-27 08:30:01.210  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom
08-27 08:30:01.210  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.210  4697  4714 D LTE_CONFIG:  Checking dir: /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global
08-27 08:30:01.211  4697  4714 D LTE_CONFIG: provisioning_params.xml not found, copy all other files from dir : /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global
01-02 03:43:01.064   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.211  4697  4714 D LTE_CONFIG: filesCount = 0
08-27 08:30:01.211  4697  4714 D LTE_CONFIG: copyLogLevelFile: emulatedPath = /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global
08-27 08:30:01.211  4697  4714 D LTE_CONFIG: copyLogLevelFile: logLevelFilePath = /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global/debug/loglevels.xml
08-27 08:30:01.211  4697  4714 D LTE_CONFIG: copyLogLevelFile: logLevels file copy failed: srcLogLevelFile = /storage/emulated/0/Android/data/com.qti.ltebc/files/embms/qcom/global/debug/loglevels.xml does not exist
08-27 08:30:01.212  4697  4714 I  Ltebc  :  ConfigurationManager () load file from internal directory failed
08-27 08:30:01.213  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.214  4697  4714 I LTE Application: Ltebc : loadConfigData() 
08-27 08:30:01.216  4697  4714 I Ltebc   :  ConfigurationManager : getProperties() 
08-27 08:30:01.216  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.216  4697  4714 E LTE Application: Ltebc : preemption_priority is available in config file () : 5
08-27 08:30:01.216  4697  4714 I LTE Application: Ltebc : loadConfigData() sPreemptionPriority :5
08-27 08:30:01.217  4697  4714 I LTE Application: Ltebc : loadConfigData() sEarFcnList :5230
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : enable_embms_sim is available in config file () : false
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : loadConfigData() enableEmbmsSim :false
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : shutdowntimer is available in config file () : 0
08-27 08:30:01.217  4697  4714 E LTE Application:  shutDownTimer : 0.0
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : provisioning_timeout_ms is available in config file () : **********
08-27 08:30:01.217  4697  4714 I LTE Application:  mProvisioningTMO : **********
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : enable_unicast_in_roaming is available in config file () 
08-27 08:30:01.217  4697  4714 I LTE Application: Ltebc : loadConfigData() enableUnicastInRoaming :false
08-27 08:30:01.217  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : default_plmn is not available in config file () 
08-27 08:30:01.217  4697  4714 E LTE Application: Ltebc : roaming_check_periodicity_ms is available in config file () 
08-27 08:30:01.217  4697  4714 I LTE Application: Ltebc : loadConfigData() roaming_check_periodicity_ms :5000
08-27 08:30:01.218  4697  4714 E LTE Application: Ltebc : embms_get_e911_state_retry_attempts is available in config file () 
08-27 08:30:01.218  4697  4714 I LTE Application: Ltebc : loadConfigData() EMBMS_GET_E911_STATE_RETRY_ATTEMPTS :600
08-27 08:30:01.218  4697  4714 E LTE Application: provisioningTimerTask: mProvisioningTMO = **********
08-27 08:30:01.218  4697  4714 I LTE Application: getStartupReason - startup_reason: STARTUP_REASON_BOOT
08-27 08:30:01.219  4697  4714 I LTEBC Init Manager: handlePermissionResult STARTUP_REASON_BOOT
08-27 08:30:01.219  4697  4714 I LTELicenseManager: startLicenseCheck requestCode: 3 cb: com.qualcomm.ltebc.LTEBCInitManager@a482a72
08-27 08:30:01.219  4697  4714 I LTELicenseManager: processLicenseCheck in progress false
01-02 03:43:01.073   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.223  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.082   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.232  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.238  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.239  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.240  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 135634846; UID 10078; state: DISABLED
08-27 08:30:01.240  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 177438394; UID 10078; state: DISABLED
08-27 08:30:01.241  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 135772972; UID 10078; state: DISABLED
08-27 08:30:01.241  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 135754954; UID 10078; state: ENABLED
01-02 03:43:01.091   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.243  4697  4714 I LTELicenseManager: MinkSocketFd connect true
08-27 08:30:01.244  4697  4714 I LTELicenseManager: Scheduling mink socket timeout 1000
08-27 08:30:01.244  4697  4714 I LTEBC Init Manager: Handled state : 7
08-27 08:30:01.244  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.244  1057  1364 D CompatibilityChangeReporter: Compat change id reported: 143937733; UID 10078; state: ENABLED
08-27 08:30:01.247  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.248  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.100   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.256  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.259   761   761 D Zygote  : Forked child process 4719
01-02 03:43:01.109   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.260  1057  1364 I ActivityManager: Start proc 4719:com.qualcomm.qti.qms.service.trustzoneaccess/u0a78 for service {com.qualcomm.qti.qms.service.trustzoneaccess/com.qualcomm.qti.qms.service.trustzoneaccess.TZAccessService}
08-27 08:30:01.262  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.268  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.118   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.270  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.273  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.274  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.272  4719  4719 W trustzoneaccess: type=1400 audit(0.0:158): avc: denied { create } for dev="anon_inodefs" ino=98214 scontext=u:r:vendor_tzas_app:s0:c78,c256,c512,c768 tcontext=u:object_r:vendor_tzas_app:s0:c78,c256,c512,c768 tclass=anon_inode permissive=0 app=com.qualcomm.qti.qms.service.trustzoneaccess
08-27 08:30:01.278  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.279  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.127   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.281   905  4692 W audio_hw_primary: out_write: underrun(1) frames_by_time(48388) > out->last_fifo_frames_remaining(384)
08-27 08:30:01.286  1877  1885 I adbd    : jdwp connection from 4719
01-02 03:43:01.136   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.290  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.293  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.296  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 168419799; UID 10078; state: DISABLED
08-27 08:30:01.296  1057  3252 D CompatibilityChangeReporter: Compat change id reported: 273564678; UID 10078; state: DISABLED
08-27 08:30:01.296  1057  3252 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:30:01.296  1057  3252 D CompatibilityInfo: applicationDensity - 160
08-27 08:30:01.296  1057  3252 D CompatibilityInfo: applicationScale - 1.0
01-02 03:43:01.145   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.299  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.300  1057  3252 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 08:30:01.300  1057  3252 D CompatibilityInfo: applicationDensity - 160
08-27 08:30:01.300  1057  3252 D CompatibilityInfo: applicationScale - 1.0
08-27 08:30:01.301  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.305  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.154   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.308  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.308  4719  4719 D CompatibilityChangeReporter: Compat change id reported: 171979766; UID 10078; state: ENABLED
08-27 08:30:01.309  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.309  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.163   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.323  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.172   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.329  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.331  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.181   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.335  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.339  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.340  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.341  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.343  4719  4719 D nativeloader: classloader namespace configured for unbundled vendor apk. library_path=/vendor/app/TrustZoneAccessService/lib/arm64:/vendor/lib64
01-02 03:43:01.190   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.199   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.354  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.357  2507  3940 D MonitorTool_MonitorAppService: [08-27 08:30:01.355] [pool-3-thread-1:36] Starting to check status of 3 apps
08-27 08:30:01.358  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.360  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.361  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:30:01.360] [pool-3-thread-1:36] App[drvsys] not detected running
08-27 08:30:01.361  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:30:01.361] [pool-3-thread-1:36] App[drvsys] running status: Not running
08-27 08:30:01.361  2507  3940 W MonitorTool_MonitorAppService: [08-27 08:30:01.361] [pool-3-thread-1:36] App[drvsys] not running, attempting to start
08-27 08:30:01.363  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.208   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.366  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.217   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.369  4719  4719 I Perf    : Connecting to perf service.
08-27 08:30:01.370  2507  3940 E MonitorTool_MonitorAppService: [08-27 08:30:01.369] [pool-3-thread-1:36] Could not get launch intent for app[drvsys], it might not be a launchable app
08-27 08:30:01.370  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.370  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.370  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:30:01.370] [pool-3-thread-1:36] App[com.thundercomm.testapp] not detected running
08-27 08:30:01.371  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:30:01.371] [pool-3-thread-1:36] App[com.thundercomm.testapp] running status: Not running
08-27 08:30:01.371  2507  3940 W MonitorTool_MonitorAppService: [08-27 08:30:01.371] [pool-3-thread-1:36] App[com.thundercomm.testapp] not running, attempting to start
08-27 08:30:01.372  2507  3940 E MonitorTool_MonitorAppService: [08-27 08:30:01.371] [pool-3-thread-1:36] Could not get launch intent for app[com.thundercomm.testapp], it might not be a launchable app
08-27 08:30:01.372  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:30:01.372] [pool-3-thread-1:36] App[com.ssol.titanApp] not detected running
08-27 08:30:01.373  2507  3940 V MonitorTool_MonitorAppService: [08-27 08:30:01.372] [pool-3-thread-1:36] App[com.ssol.titanApp] running status: Not running
08-27 08:30:01.373  2507  3940 W MonitorTool_MonitorAppService: [08-27 08:30:01.373] [pool-3-thread-1:36] App[com.ssol.titanApp] not running, attempting to start
08-27 08:30:01.374  2507  3940 E MonitorTool_MonitorAppService: [08-27 08:30:01.374] [pool-3-thread-1:36] Could not get launch intent for app[com.ssol.titanApp], it might not be a launchable app
08-27 08:30:01.374  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.378  4719  4719 V GraphicsEnvironment: ANGLE Developer option for 'com.qualcomm.qti.qms.service.trustzoneaccess' set to: 'default'
01-02 03:43:01.226   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.379  4719  4719 V GraphicsEnvironment: ANGLE GameManagerService for com.qualcomm.qti.qms.service.trustzoneaccess: false
08-27 08:30:01.384  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.385  4719  4719 D NetworkSecurityConfig: No Network Security Config specified, using platform default
08-27 08:30:01.385  4719  4719 D NetworkSecurityConfig: No Network Security Config specified, using platform default
08-27 08:30:01.390  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.235   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.392  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.394  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.244   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.396  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.400  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.401  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.406  4697  4697 I MinkSocketFd: connected
08-27 08:30:01.408  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.410  4719  4735 V CredentialHelper: Processing credentials for uid = 10127
08-27 08:30:01.412   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4188 went down
08-27 08:30:01.415  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.253   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.262   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.418   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4189 went down
08-27 08:30:01.421  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.271   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.421   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418a went down
08-27 08:30:01.425  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.425  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.427  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.431  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.431  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.281   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.441   904  4737 I minksocket: MinkSocket_recvInvocationRequest: SMCINVOKE_IOCTL_LOG peer gid=10078 uid=10078 invoke=1, o.invoke=0x60ec729eb8, o.context=0xb400006e3c1d03c0
01-02 03:43:01.290   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.442  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.443   904  4737 I ssgtzd  : served new env
08-27 08:30:01.445  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.448  4697  4697 V MinkSockJni: fd failed check: -755728016
08-27 08:30:01.449  4697  4697 I LTELicenseManager: onConnectionError called OBJECT_INVALID
08-27 08:30:01.451  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.299   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.453  1057  1209 D OomAdjuster: Not killing cached processes
08-27 08:30:01.455  4697  4697 I LTELicenseManager: onDisconnect called
08-27 08:30:01.455  4697  4697 I LTELicenseManager: saveLicenseInSharedPreferences false
08-27 08:30:01.455  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.457  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.308   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.459  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.461  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.461  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.466  4697  4697 D LTELicenseManager: processLicenseCheck notify caller license validity false
08-27 08:30:01.467  4697  4697 I LTEBC Init Manager: onLicenseCheckComplete 3 isValid false
08-27 08:30:01.467  4697  4697 V LTE Application: initStateChanged - LTEBC_INIT_LICENSE_ERROR
08-27 08:30:01.467  4697  4697 I LTE Application: Error during ltebc initialization
01-02 03:43:01.317   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.471  4697  4697 I LTE-FD Service:: LTEFileDeliveryServiceHelper get connection manager : Thread[Thread-2,5,main]
08-27 08:30:01.472  4697  4697 I LTE Application: isAnyAppBindedWithMW : No Application is binded with MW
08-27 08:30:01.472  4697  4697 I LTEBC Init Manager: onLicenseCheckComplete failed, hence calling explicit stop
08-27 08:30:01.472  4697  4697 I LTE Application: explicitStop initState LTEBC_INIT_LICENSE_ERROR
08-27 08:30:01.475  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.326   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.476  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.477  4697  4714 I LTEBC Init Manager: handleStartDeInitialization()
08-27 08:30:01.477  4697  4714 I LTE Application: isAnyAppBindedWithMW : No Application is binded with MW
08-27 08:30:01.477  4697  4714 I LTEBC Init Manager: forceExit() Force Killing MSP as sKeepAlive is not ZERO, sKeepAlive:1
08-27 08:30:01.477  4697  4714 I LTE Application:  shutdownApp
08-27 08:30:01.477  4697  4714 I LTE Application:  shutdownApp : after jshutdown() 
08-27 08:30:01.477  4697  4714 I LTE Application:  shutdownApp : calling Disconnect from Ebmbs 
08-27 08:30:01.478  4697  4714 D LTE Application: IntentSender : Last intent Status INACTIVE_SENT forcedBroadcast flag = false
08-27 08:30:01.478  4697  4714 I LTE Application:  shutdownApp : calling Connection Mgr shutdown() 
08-27 08:30:01.478  4697  4714 W SOCEKT  : Connection Thread shutdown
08-27 08:30:01.478  4697  4714 W SOCEKT  : Recieve thread Pool shutdown
08-27 08:30:01.479  4697  4697 I LTE Root Service: onDestroy
08-27 08:30:01.481  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.482  4697  4714 I LTE Application:  shutdownApp : calling Ltebc exit 
08-27 08:30:01.486  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.488  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.335   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.491  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.491  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.493  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.494  4697  4714 I com.qti.ltebc: System.exit called, status: 0
08-27 08:30:01.494  4697  4714 I AndroidRuntime: VM exiting with result code 0, cleanup skipped.
01-02 03:43:01.344   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.506  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.510  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.512  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.353   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.362   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.516  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.371   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.518  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.522  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.522  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.527  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.380   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.536  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.389   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.543  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.544  1057  3252 I ActivityManager: Process com.qti.ltebc (pid 4697) has died: cch+5 CEM 
08-27 08:30:01.544  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.544  1057  1376 I libprocessgroup: Successfully killed process cgroup uid 10127 pid 4697 in 0ms
08-27 08:30:01.545   761   761 I Zygote  : Process 4697 exited cleanly (0)
08-27 08:30:01.547  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.398   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.549  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.551  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:30:01.551  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:30:01.551  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:30:01.552   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:01.552   731   731 I mpu_uart: [TIME-:24]:create
08-27 08:30:01.552  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.552  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.407   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.561  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.566  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.416   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.573  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.425   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.577  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.579  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.582  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.582  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.434   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.443   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.597  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.603  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.452   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.607  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.609  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.611  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.462   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.613  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.613  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.471   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.627  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.480   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.633  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.637  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.640  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.489   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.643  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.644  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.645  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.498   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.657  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.507   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.664  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.668  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.516   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.670  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.674  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.674  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.525   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.534   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.688  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.694  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.695  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.698  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.543   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.700  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.704  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.704  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.561   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.718  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.570   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.726  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.728  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.730  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.579   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.734  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.735  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.588   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.745  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.748  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.597   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.756  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.759  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.606   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.761  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.765  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.765  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.615   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.624   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.779  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.633   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.786  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.789  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.791  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.795  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.643   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.796  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.798  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.652   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.809  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.661   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.817  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.819  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.821  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.670   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.825  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.828  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.679   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.839  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.688   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.847  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.847  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.851  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.851  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.855  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.697   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.858  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.706   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.715   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.870  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.724   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.877  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.881  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.882  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.733   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.886  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.888  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.898  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.900  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.750   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.908  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.911  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.912  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.759   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.915  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.916  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.919  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.768   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.774     7     7 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
08-27 08:30:01.924  1053  1053 D vendor.qti.vibrator: Vibrator on for timeoutMs: 20
08-27 08:30:01.925  1053  4745 D vendor.qti.vibrator: Starting on on another thread
08-27 08:30:01.931   905  4692 W audio_hw_primary: out_write: underrun(2) frames_by_time(29294) > out->last_fifo_frames_remaining(384)
08-27 08:30:01.933  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.782   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.783     7     7 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
01-02 03:43:01.792   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.792     7     7 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
08-27 08:30:01.943  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.943  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.945  1053  4745 D vendor.qti.vibrator: Notifying on complete
08-27 08:30:01.942  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.946  1053  1053 D vendor.qti.vibrator: QTI Vibrator off
08-27 08:30:01.948  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.800   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.952  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.952  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.805   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.964  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.814   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.966  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:01.973  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.973  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.975  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.823   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.982  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.982  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.832   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.841   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:01.995  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:01.999  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.850   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.004  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.004  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.005  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.859   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.013  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.013  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.018  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.868   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.025  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.877   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.034  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.034  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.036  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.886   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.043  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.043  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.895   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.050  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.904   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.058  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.061   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:02.064  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.064  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.066  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.913   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.067  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.922   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.074  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.074  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.081   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:30:02.081   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:02.081   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:43:01.931   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.089  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.940   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.095  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.095  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.096  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.949   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.100  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.104  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.104  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.958   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.118  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.967   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.119  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.125  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.125  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.127  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:01.976   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.134  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.134  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.135  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:01.985   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:01.994   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.151  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.155  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.156  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.003   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.157  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.165  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.165  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.219  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.066   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.220  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.226  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.227  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.075   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.085   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.242  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.093   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.246  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.248  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.250  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.253  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.256  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.257  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.103   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.112   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.270  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.121   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.274  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.277  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.278  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.280  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.130   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.287  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.287  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.287  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.139   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.148   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.304  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.305  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.307  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.309  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.157   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.310  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.166   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.317  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.318  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.321  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.175   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.335  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.184   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.337  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.338  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.339  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.341  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.193   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.348  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.349  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.202   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.211   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.366  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.369  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.371  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.372  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.220   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.373  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.380  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.380  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.229   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.238   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.396  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.400  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.247   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.402  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.402  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.406  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.256   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.411  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.411  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.408  1598  1598 I tlog    : type=1400 audit(0.0:159): avc: denied { rename } for name="-00001_persist_00019_250827_082957.log.ing" dev="dm-6" ino=14664 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:30:02.408  1598  1598 I tlog    : type=1400 audit(0.0:160): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
08-27 08:30:02.412   578   578 W hwservicemanage: type=1400 audit(0.0:161): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:30:02.418  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
01-02 03:43:02.265   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.418  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:02.418  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:43:02.267   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:43:02.267   578   578 I binder  : send failed reply for transaction 60887 to 1598:1620
08-27 08:30:02.421   904   930 E ssgtzd  : Modemcomm: Attempt 2 : Init Instance failed with -3
08-27 08:30:02.420   578   578 W hwservicemanage: type=1400 audit(0.0:162): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:30:02.424  1598  1620 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:30:02.424  1598  1620 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:02.424  1598  1620 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:43:02.273   578   578 I binder  : 578:578 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:43:02.273   578   578 I binder  : send failed reply for transaction 60893 to 1598:1620
01-02 03:43:02.274   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.426  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.430  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.432  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.432  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.432  1598  1598 I tlog    : type=1400 audit(0.0:163): avc: denied { setattr } for name="-00001_persist_00020_250827_083002.log.ing" dev="dm-6" ino=14667 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
01-02 03:43:02.283   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.440  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.441  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.441  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.292   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.301   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.456  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.457  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.460  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.463  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.463  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.310   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.471  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.471  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.319   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.329   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.487  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.338   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.491  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.491  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.493  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.493  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.347   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.502  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.502  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.356   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.365   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.518  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.521  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.523  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.374   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.524  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.525  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.532  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.532  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.383   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.540  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.392   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.549  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.551  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.401   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.554  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.556  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.557  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.410   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.562  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.563  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.419   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.574  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.579  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.428   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.582  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.584  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.586  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.437   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.591  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.593  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.593  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.446   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.455   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.609  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.611  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.612  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.614  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.616  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.464   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.623  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.623  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.473   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.482   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.643  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.643  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.643  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.491   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.645  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.647  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.500   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.654  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.654  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.659  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.509   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.518   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.673  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.673  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.676  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.677  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.677  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.527   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.684  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.684  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.536   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.693  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.546   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.704  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.704  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.706  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.708  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.555   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.710  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.715  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.716  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.564   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.573   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.727  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.582   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.734  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.734  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.737  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.738  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.591   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.744  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.745  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.747  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.600   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.609   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.762  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.765  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.765  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.767  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.768  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.618   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.776  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.777  2643  4746 D ConfigTool_ConfigToolService: External backup attempt 1/3
08-27 08:30:02.778  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.627   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.779  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.780  2643  4746 D ConfigTool_ConfigToolService: Creating external backup of encrypted configuration
08-27 08:30:02.780  2643  4746 D ConfigTool_ConfigToolService: >>> BEGIN: Backup encrypted config to external storage <<<
08-27 08:30:02.781  2643  4746 D ConfigTool_ConfigToolService: External backup file path: /storage/9EE7-1000/Config/config.bin
01-02 03:43:02.636   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.795  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.795  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.795  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.797  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.799  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.645   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.654   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.806  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.809  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.812  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.663   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.672   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.826  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.826  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.828  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.829  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.830  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.681   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.836  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.839  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.690   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.699   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.856  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.856  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.858  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.859  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.708   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.863  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.867  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.717   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.869  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.872  2643  4746 I ConfigTool_ConfigToolService: Encrypted config file backed up to external storage successfully
08-27 08:30:02.872  2643  4746 D ConfigTool_ConfigToolService: >>> END: Backup encrypted config to external storage - SUCCESS <<<
08-27 08:30:02.873  2643  4746 I ConfigTool_ConfigToolService: External backup successful on attempt 1
01-02 03:43:02.726   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.880  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.886  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.887  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.889  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.735   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.890  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.744   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.897  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.897  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.900  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.753   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.914  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.917  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.917  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.919  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.920  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.772   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.927  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.930  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.931  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.781   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.790   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.947  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.947  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.948  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.950  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.951  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.799   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.958  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.961  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.808   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.817   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.826   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.978  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.978  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.980  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.981  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:02.982  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:02.988  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.835   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:02.991  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.844   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.853   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.008  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.008  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.010  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.012  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.862   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.019  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.021  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.871   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.880   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.033  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.039  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.039  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.040  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.889   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.042  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.049  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.898   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.052  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.907   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.061   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-02 03:43:02.916   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.069  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.070  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.071  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.073  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.925   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.079  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.081   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:30:03.082   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:03.082   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:03.082  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.084  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:02.934   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.943   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.100  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.100  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.101  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.103  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.952   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.110  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.113  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.961   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:02.970   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.130  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.130  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.131  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.979   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.133  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.134  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.140  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.988   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.143  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:02.997   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.152  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.006   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.161  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.161  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.162  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.164  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.167   905  4692 W audio_hw_primary: out_write: underrun(3) frames_by_time(48197) > out->last_fifo_frames_remaining(384)
01-02 03:43:03.015   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.170  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.174  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.024   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.033   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.185  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.191  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.191  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.192  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.194  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.043   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.201  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.203  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.204  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.051   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.060   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.219  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.224  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.224  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.224  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.222  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.231  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.079   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.235  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.236  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.088   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.097   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.253  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.254  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.254  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.255  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.255  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.106   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.262  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.266  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.115   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.270  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.124   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.284  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.285  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.285  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.133   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.287  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.288  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.292  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.142   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.296  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.151   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.160   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.315  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.315  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.316  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.317  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.169   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.322  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.324  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.326  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.178   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.187   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.345  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.346  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.346  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.348  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.196   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.354  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.355  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.205   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.357  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.214   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.376  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.376  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.376  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.223   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.378  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.384  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.232   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.388  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.389  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.241   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.406  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.406  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.406  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.407  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.408  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.258   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.415  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.418  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.267   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.422  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.424   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418b went down
08-27 08:30:03.428   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418c went down
01-02 03:43:03.276   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.434   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418d went down
08-27 08:30:03.436  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.437  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.437  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.285   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.439  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.439  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.445  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.449  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.294   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.303   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.456  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.312   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.467  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.467  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.468  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.469  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.322   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.474  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.475  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.479  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.331   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.491  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.340   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.497  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.497  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.498  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.499  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.349   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.506  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.508  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.509  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.528  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.528  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.376   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.530  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.536  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.385   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.540  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.542  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.394   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.403   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.558  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.558  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.559  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.560  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.561  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.412   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.567  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.570  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.421   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.576  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.430   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.588  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.588  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.591  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.592  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.439   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.597  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.601  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.448   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.457   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.610  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.466   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.619  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.619  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.622  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.622  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.626  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.627  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.475   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.631  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.484   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.493   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.644  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.649  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.650  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.652  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.653  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.502   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.658  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.661  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.662  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.511   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.520   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.678  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.680  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.680  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.529   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.683  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.684  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.689  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.692  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.539   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.695  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.548   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.557   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.710  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.711  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.712  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.714  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.715  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.566   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.719  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.723  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.729  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.575   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.584   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.741  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.741  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.744  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.593   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.745  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.746  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.750  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.753  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.602   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.611   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.763  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.771  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.771  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.620   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.774  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.776  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.779  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.781  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.629   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.783  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.638   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.647   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.802  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.802  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.805  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.806  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.656   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.811  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.813  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.814  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.665   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.674   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.831  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.832  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.833  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.835  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.836  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.683   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.841  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.844  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.692   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.847  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.701   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.863  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.863  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.710   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.864  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.866  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.867  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.872  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.719   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.875  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.728   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.882  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.737   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.893  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.893  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.896  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.897  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.898  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.746   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.903  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.905  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.755   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.915  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.764   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.924  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.924  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.774   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.927  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.927  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.932  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.933  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.783   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.938  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.792   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.949  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.801   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.954  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.954  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.957  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.958  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.810   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.963  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.967  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:03.969  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.819   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.828   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.985  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.985  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.988  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.988  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.837   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.994  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.846   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:03.999   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:30:03.999   731   747 I mpu_uart: [TIME-:23]:delete
08-27 08:30:03.999   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x32, 0x38, 0x31, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x22, ]
08-27 08:30:03.999   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20281,current_state=0, ota_state = 1
08-27 08:30:03.999   731   745 V mpu_uart:  >> log: 
08-27 08:30:04.000  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.855   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.014   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:30:04.015   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x31, 0x35, 0x20, 0x6d, 0x76, 0xa, 0x70, ]
08-27 08:30:04.015   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12315 mv
08-27 08:30:04.015   731   745 V mpu_uart:  >> log: 
08-27 08:30:04.015  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.015  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.864   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.018  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.018  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.019  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.019   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:30:04.019   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:30:04.024  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.873   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.028   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:30:04.028   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:30:04.028   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:30:04.028   731   745 V mpu_uart:  >> log: 
08-27 08:30:04.030   731   731 I mpu_uart: [TIME-:24]:delete
08-27 08:30:04.031  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.031  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:30:04.032  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:30:04.033   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:04.033   731   731 I mpu_uart: [TIME-:25]:create
01-02 03:43:03.882   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.038   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:04.038   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:30:04.038   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:04.039  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:30:04.040  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:30:04.040  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
01-02 03:43:03.891   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.046  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.046  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.048   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:30:04.048   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:30:04.048  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.049   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:30:04.049   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x46, 0xdd, ]
08-27 08:30:04.049  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.050   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:04.051  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x46 
08-27 08:30:04.051  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.052  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:30:04.053  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
01-02 03:43:03.900   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.054  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12358
08-27 08:30:04.054  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.055  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12358
08-27 08:30:04.059   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:30:04.059   731   731 I mpu_uart: [TIME-:25]:delete
08-27 08:30:04.059   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 08:30:04.059   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
01-02 03:43:03.909   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.062  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.063  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 08:30:04.063  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:30:04.063  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:30:04.063  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 08:30:04.063  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 08:30:04.063  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
01-02 03:43:03.918   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.070  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:30:04.076  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.076  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.079  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.079  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.927   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.085  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.085  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:03.936   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.092  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.945   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.106  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.106  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.954   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.109  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.110  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.115  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.963   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.123  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.973   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:03.982   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.136  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.137  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.137  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.140  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.140  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:03.991   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.146  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.000   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.153  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.009   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.167  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.167  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.018   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.170  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.171  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.176  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.027   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.183  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.186  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.036   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.045   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.198  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.198  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.200  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.201  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.054   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.206  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.207   905  4692 W audio_hw_primary: out_write: underrun(4) frames_by_time(48081) > out->last_fifo_frames_remaining(384)
08-27 08:30:04.214  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.063   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.072   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.228  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.228  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.231  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.232  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.081   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.237  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.237  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.090   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.249  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.099   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.255  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.258  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.258  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.261  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.262  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.267  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.117   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.279  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.126   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.288  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.135   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.289  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.292  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.293  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.144   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.297  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.153   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.305  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.309  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.162   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.319  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.319  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.321  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.323  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.172   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.324  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.328  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.180   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.339  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.340  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.190   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.350  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.350  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.353  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.354  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.370  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.373  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.226   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.380  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.380  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.384  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.384  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.388  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.235   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.244   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.400  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.253   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.406  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.411  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.411  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.262   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.414  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.415  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.418  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.423  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.271   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.431  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.433   904   930 E ssgtzd  : Modemcomm: Attempt 3 : Init Instance failed with -3
01-02 03:43:04.280   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.441  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.441  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.289   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.445  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.445  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.298   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.449  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.457  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.460  1057  1363 I ActivityManager: Waited long enough for: ServiceRecord{ffc28fb u0 com.qualcomm.qtil.btdsda/.BtDsDaService}
01-02 03:43:04.307   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.462  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.316   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.471  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.473  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.474  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.475  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.476  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.325   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.479  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.334   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.491  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.492  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.343   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.502  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.504  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.352   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.506  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.506  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.510  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.361   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.370   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.523  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.524  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.532  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.379   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.534  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.536  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.537  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.540  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.388   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.541  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:30:04.542  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.542  2543  2743 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:30:04.544  3511  3822 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:30:04.545  3511  3807 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:30:04.546  3511  3807 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:30:04.546  3511  3807 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@918cded
08-27 08:30:04.547  3511  3807 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:30:04.547  3511  3807 I EventProvider: [EventProviderApp] responseAccState: accOn=false
01-02 03:43:04.397   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.550  2543  2743 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:30:04.551  2543  2743 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:30:04.552  1057  1209 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3511:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:30:04.552  1057  1209 E ActivityManager: java.lang.Throwable
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:30:04.552  1057  1209 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:30:04.553  2543  2743 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:30:04.553  2543  2743 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:30:04.554   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:30:04.554   731   731 I mpu_uart: [TIME-:26]:create
08-27 08:30:04.555  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.558  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.406   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.563  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.565  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.567  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.567  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.568   731   744 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:30:04.568   731   744 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
01-02 03:43:04.415   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.571  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.571   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:04.571   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:30:04.571   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:30:04.571   731   731 I mpu_uart: [TIME-:26]:delete
08-27 08:30:04.572  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:30:04.572  2543  2743 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:30:04.572  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:30:04.573   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:04.573  2543  2742 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:30:04.573   731   731 I mpu_uart: [TIME-:27]:create
08-27 08:30:04.575  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.424   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.433   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.585  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.591   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:04.591   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:04.592  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.593  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.595  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.442   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.597  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.597  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.601  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.451   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.460   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.616  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.469   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.624  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.626  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.626  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.627  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.628  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.631  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.478   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.488   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.644  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.646  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.497   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.654  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.656  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.506   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.658  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.658  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.662  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.515   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.677  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.524   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.677  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.685  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.533   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.686  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.688  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.689  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.692  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.542   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.695  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.551   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.707  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.560   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.715  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.717  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.718  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.719  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.569   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.722  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.578   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.728  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.738  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.587   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.745  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.745  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.747  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.749  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.596   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.751  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.753  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.605   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.762  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.768  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.614   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.776  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.623   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.778  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.779  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.779  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.781  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.783  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.632   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.641   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.796  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.799  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.650   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.806  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.809  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.810  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.812  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.659   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.814  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.814  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.668   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.830  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.677   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.831  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.836  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.687   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.840  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.841  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.842  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.844  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.847  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.696   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.705   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.860  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.714   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.867  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.870  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.872  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.873  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.874  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.723   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.881  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.732   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.890  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.741   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.897  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.898  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.900  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.750   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.902  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.903  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.905  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.915  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.767   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.921  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.928  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.776   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.931  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.932  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.933  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.935  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.785   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.794   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.949  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:04.952  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.803   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.958  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.961  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.963  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.964  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.812   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.966  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.821   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.830   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.982  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.983  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.839   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:04.990  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.992  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.994  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.995  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.996  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.848   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.857   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.013  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.016  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.020  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.866   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.022  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.024  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.025  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.027  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.875   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.033  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.884   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.043  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.893   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.051  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.051  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.902   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.054  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.055  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.055  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.057  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.060  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:30:05.060  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:30:05.061  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:30:05.062   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:05.063   731   747 I mpu_uart: [TIME-:28]:create
01-02 03:43:04.911   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.067  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.921   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.074  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.929   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.081  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.085  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.085  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.086  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.088  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.939   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:04.947   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.102  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.104  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.957   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.111  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.115  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.115  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.117  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.966   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.118  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.975   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.134  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.136  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:04.984   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.142  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:04.993   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.146  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.146  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.148  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.148  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.002   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.165  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.011   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.169  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.172  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.020   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.176  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.177  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.178  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.179  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.029   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.038   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.195  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.047   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.203  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.207  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.207  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.208  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.209  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.056   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.065   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.220  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:05.074   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.226  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.233  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.083   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.237  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.237  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.239  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.240  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.092   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.249   905  1024 D audio_hw_primary: out_standby: enter: stream (0xee0c64c0) usecase(1: low-latency-playback)
01-02 03:43:05.099  1024  1024 I [Awinic][1-0034]aw882xx_mute: mute state=1
01-02 03:43:05.099  1024  1024 I [Awinic][1-0034]aw882xx_monitor_stop: enter
01-02 03:43:05.100  1024  1024 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-02 03:43:05.101  1024  1024 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-02 03:43:05.101   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.102  1024  1024 I [Awinic][1-0034]aw_dev_uls_hmute: done
08-27 08:30:05.256  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.110   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.264  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.268  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.268  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.270  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.119   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.271  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.273  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.128   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.287  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.137   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.140  1024  1024 I [Awinic][1-0034]aw_dev_mute: done
08-27 08:30:05.294  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.144  1024  1024 I [Awinic][1-0034]aw_dev_amppd: done
01-02 03:43:05.144  1024  1024 I [Awinic][1-0034]aw_dev_pwd: done
01-02 03:43:05.144  1024  1024 I [Awinic][1-0034]aw882xx_device_stop: done
01-02 03:43:05.146   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.298  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.298  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.300  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.311  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.152  1024  1024 E msm_adsp_clean_mixer_ctl_adm_pp_event_queue: failed to get kctl.
01-02 03:43:05.155   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.164   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.317  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.166  1024  1024 I [Awinic][1-0034]aw882xx_shutdown: stream playback
08-27 08:30:05.321  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.325  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.173   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.328  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.329  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.330  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.182   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.341  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.192   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.347  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.201   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.355  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.359  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.359   905  1024 D audio_hw_primary: disable_audio_route: reset and update mixer path: low-latency-playback
08-27 08:30:05.359  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.361  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.210   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.361   905  1024 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 2, with Raise=0
08-27 08:30:05.361   905  1024 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 08:30:05.361   905  1024 D audio_hw_primary: disable_snd_device: snd_device(2: speaker)
08-27 08:30:05.361   905  1024 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 0, with Raise=0
01-02 03:43:05.219   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.372  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.374  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.378  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.228   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.386  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.389  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.237   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.390  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.391  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.246   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.404  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.408  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.255   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.416  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.264   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.419  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.422  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.422  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.423  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:05.273   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.434  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.282   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.438   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418e went down
08-27 08:30:05.439  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.441   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418f went down
01-02 03:43:05.291   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.445   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4190 went down
08-27 08:30:05.446  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.450  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.452  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.453  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.300   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.456  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:05.309   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.465  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.469  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.318   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.477  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.481  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.327   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.482  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.483  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.336   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.495  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.345   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.500  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.354   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.508  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.509  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.511  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.513  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.514  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.363   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.373   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.525  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.525  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.530  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.381   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.540  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.541  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.543  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.544  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.391   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.400   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.556  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.558  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.560  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.409   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.418   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.570  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.572  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.573   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:05.573  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.574  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.427   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.586  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.436   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.591  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.592   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:30:05.592  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.592   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:05.592   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:43:05.445   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.602  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.603  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.604  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.605  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.454   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.609  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.617  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.463   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.621  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.472   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.626  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.632  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.633  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.481   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.634  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.636  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.643  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:05.490   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.647  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.652  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.499   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.508   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.662  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.664  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.664  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.666  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.517   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.526   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.677  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.679  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.682  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.535   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.693  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.694  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.694  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.695  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.697  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.544   554   554 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:43:05.553   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.709  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.711  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.712  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.562   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.723  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.725  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.571   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.725  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.727  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.580   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.740  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.589   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.744  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.745  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:43:05.598   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.754  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.756  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.755  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.758  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.607   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.762  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.767  1053  1053 D vendor.qti.vibrator: QTI Vibrator off
08-27 08:30:05.770  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.774  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.624   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.778  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.784  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:05.633   554   554 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.786  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.786  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.788  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.795  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.800  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.804  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.812  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.814  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.816  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.816  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.819  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.829  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.831  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.835  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.845  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.847  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.847  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.847  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.849  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.862  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.863  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.866  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.875  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.877  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.878  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.880  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.880  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.892  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.896  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.898  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.906  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.908  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.908  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.910  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.915  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.923  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.927  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.932  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.936  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.939  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.941  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.941  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.949  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.954  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.957  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.965  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:05.967  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.969  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.971  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.971  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.984  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.987  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.997  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.999  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.000  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.001  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.002  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.014  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.016  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.018  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.028  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.030  1057  1348 I DropBoxManagerService: add tag=system_app_strictmode isTagEnabled=true flags=0x2
08-27 08:30:06.031  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.032  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.032  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.034  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.045  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.048  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.051  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.058  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.061  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.062  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.063  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.068  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.075  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.079  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.089  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.092  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.093  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.093  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.106  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.109  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.119  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.122  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.124  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.124  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.136  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.140  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.150  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.153  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.154  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.154  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.167  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.170  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.180  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.183  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.185  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.185  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.197  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.200  1033  1033 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.204  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.211  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.214  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.215  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.215  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.228  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.235  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.241  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.245  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.246  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.247  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.258  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.265  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.272  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.275  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.277  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.277  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.289  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.296  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.302  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.306  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.307  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.308  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.320  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.326  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.333  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.336  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.338  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.338  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.350  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.357  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.363  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.366  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.368  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.369  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.380  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.387  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.394  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.397  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.398  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.399  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.411  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.418  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.424  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.427  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.429  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.430  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.442  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.446   904   930 E ssgtzd  : Modemcomm: Attempt 4 : Init Instance failed with -3
08-27 08:30:06.448  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.455  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.458  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.460  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.460  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.472  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.478  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.485  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.488  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.490  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.491  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.503  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.509  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.516  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.519  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.521  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.521  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.533  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.539  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.546  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.549  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.552  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.552  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.564  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.570  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.573   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:06.577  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.580  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.582  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.582  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.592   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:30:06.593   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:06.593   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:06.594  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.600  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.607  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.610  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.613  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.613  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.625  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.631  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.638  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.641  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.644  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.644  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.656  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.661  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.668  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.672  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.674  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.674  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.686  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.692  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.699  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.702  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.705  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.705  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.717  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.722  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.729  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.733  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.735  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.736  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.747  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.753  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.760  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.763  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.766  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.766  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.777  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.783  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.790  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.794  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.796  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.797  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.808  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.814  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.821  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.824  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.827  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.827  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.838  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.845  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.851  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.855  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.857  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.858  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:43:06.711   331   331 I (virq   : irq_count)- 3:60903 217:44230 47:18871 57:8893 298:8809 300:5811 10:5030 313:4102 263:4008 223:2344
01-02 03:43:06.711   331   331 I (cpu    : irq_count)- 0:103739 1:23072 2:13201 3:8249 4:6157 5:6614 6:6776 7:7549
01-02 03:43:06.711   331   331 I (ipi    : irq_count)- 0:133421 1:54361 2:0 3:0 4:0 5:52086 6:0
08-27 08:30:06.869  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.875  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.882  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.885  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.888  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.889  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.899  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.906  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.912  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.916  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.918  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.920  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.930  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.936  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.943  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.946  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.949  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.950  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.960  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.967  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.973  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.977  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.979  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.981  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.991  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.997  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.004  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.007  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.010  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.012  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.021  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.028  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.034  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.038  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.040  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.042  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.052  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.058  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.065  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.068  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.071  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.073  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.082  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.089  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.095  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.099  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.101  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.104  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.113  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.120  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.126  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.129  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.132  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.134  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.143  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.150  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.156  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.160  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.162  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.165  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.174  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.181  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.187  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.190  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.193  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.195  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.204  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.211  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.217  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.221  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.223  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.226  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.235  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.242  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.248  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.252  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.254  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.256  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.265  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.272  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.278  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.282  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.284  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.287  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.296  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.303  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.309  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.313  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.315  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.317  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.326  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.333  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.340  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.343  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.345  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.348  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.357  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.364  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.370  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.374  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.376  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.378  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.387  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.394  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.401  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.404  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.406  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.409  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.418  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.425  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.431  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.435  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.437  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.439  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.446   904   930 E ssgtzd  : Modemcomm: Failed to get handle of QMSCA service
08-27 08:30:07.448  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.453   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4191 went down
08-27 08:30:07.453   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4192 went down
08-27 08:30:07.455   904   960 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4193 went down
08-27 08:30:07.455  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.462  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.465  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.467  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.470  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.479  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.486  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.492  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.496  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.498  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.498   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:30:07.499   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x32, 0x38, 0x32, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x21, ]
08-27 08:30:07.499   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20282,current_state=0, ota_state = 1
08-27 08:30:07.499   731   745 V mpu_uart:  >> log: 
08-27 08:30:07.499   731   731 I mpu_uart: [TIME-:27]:delete
08-27 08:30:07.500  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.510  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.514   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:30:07.515   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x37, 0x32, 0x20, 0x6d, 0x76, 0xa, 0x79, ]
08-27 08:30:07.515   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12372 mv
08-27 08:30:07.515   731   745 V mpu_uart:  >> log: 
08-27 08:30:07.516  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.519   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:30:07.519   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:30:07.522  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.526  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.528   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:30:07.528   731   747 I mpu_uart: [TIME-:28]:delete
08-27 08:30:07.528  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.528   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:30:07.528   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:30:07.528   731   745 V mpu_uart:  >> log: 
08-27 08:30:07.529  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:30:07.530  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:30:07.530   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:07.530   731   747 I mpu_uart: [TIME-:29]:create
08-27 08:30:07.531  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.538   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:07.538   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:30:07.539   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:07.540  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:30:07.540  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:30:07.540  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:30:07.540  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.547  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.548   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:30:07.549   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:30:07.549   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:30:07.549   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x46, 0xdd, ]
08-27 08:30:07.550   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:07.551  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x46 
08-27 08:30:07.551  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:30:07.551  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:30:07.551  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12358
08-27 08:30:07.551  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12358
08-27 08:30:07.553  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.557  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.559  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.559   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:30:07.560   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 08:30:07.560   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:07.560   731   747 I mpu_uart: [TIME-:29]:delete
08-27 08:30:07.561  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 08:30:07.561  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.561  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:30:07.562  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:30:07.562  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 08:30:07.562  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
08-27 08:30:07.562  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 08:30:07.564  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:30:07.571  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.577  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.584  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.587  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.589  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.592  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.601  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.608  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.614  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.618  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.620  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.622  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.632  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.638  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.645  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.648  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.650  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.653  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.662  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.668  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.675  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.679  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.681  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.683  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.693  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.699  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.706  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.709  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.711  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.714  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.723  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.730  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.736  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.740  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.742  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.744  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.754  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.760  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.767  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.770  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.773  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.775  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.784  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.791  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.797  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.801  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.803  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.805  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.814  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.821  1144  1167 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.828  1144  1169 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.832  1144  1166 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.834  1144  1170 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.836  1144  1165 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.845  1144  1168 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
