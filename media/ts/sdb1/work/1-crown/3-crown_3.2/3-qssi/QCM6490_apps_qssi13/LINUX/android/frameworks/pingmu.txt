--------- beginning of kernel
01-01 13:01:27.570   575   575 I logd    : logdr: UID=2000 GID=2000 PID=4852 b tail=0 logMask=99 pid=0 start=0ns deadline=0ns
--------- beginning of main
08-27 09:26:45.060   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 09:26:45.079   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 09:26:45.080   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:45.080   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 09:26:46.062   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 09:26:46.082   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 09:26:46.082   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:46.082   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 09:26:46.765  1591  1604 E tlogd   : [StorageChecker](hasStorageSpace) space: (used: 246M, free: 33987M), path: /data/tlog/persist/
08-27 09:26:46.986   725   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 09:26:46.987   725   742 I mpu_uart: [TIME-:3911]:delete
08-27 09:26:46.987   725   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x35, 0x31, 0x36, 0x39, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x12, ]
08-27 09:26:46.987   725   738 V mpu_uart: mcu_info:s_log_print_cnt=5169,current_state=0, ota_state = 1
08-27 09:26:46.987   725   738 V mpu_uart:  >> log: 
08-27 09:26:47.002   725   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 09:26:47.002   725   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x34, 0x30, 0x31, 0x20, 0x6d, 0x76, 0xa, 0x72, ]
08-27 09:26:47.002   725   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12401 mv
08-27 09:26:47.002   725   738 V mpu_uart:  >> log: 
08-27 09:26:47.007   725   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 09:26:47.007   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 09:26:47.015   725   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 09:26:47.016   725   741 I mpu_uart: [TIME-:3912]:delete
08-27 09:26:47.016   725   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 09:26:47.017   725   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 09:26:47.017   725   738 V mpu_uart:  >> log: 
08-27 09:26:47.018  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 09:26:47.018  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 09:26:47.019   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:47.019   725   742 I mpu_uart: [TIME-:3913]:create
08-27 09:26:47.024   725   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 09:26:47.024   725   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 09:26:47.025   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:47.026  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 09:26:47.026  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 09:26:47.027  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 09:26:47.035   725   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 09:26:47.035   725   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x63, 0xf8, ]
08-27 09:26:47.035   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:47.036  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x63 
08-27 09:26:47.037   725   737 E mpu_uart: send_buff: 3c0700111491af
08-27 09:26:47.037   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 09:26:47.037  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 09:26:47.037  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 09:26:47.037  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12387
08-27 09:26:47.038  2486  2704 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12387
08-27 09:26:47.046   725   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 09:26:47.047   725   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 09:26:47.047   725   742 I mpu_uart: [TIME-:3913]:delete
08-27 09:26:47.047   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:47.048  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 09:26:47.048  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 09:26:47.048  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 09:26:47.048  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 09:26:47.049  2486  2704 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 09:26:47.049  2486  2704 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 09:26:47.051  2486  2704 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 09:26:47.528  2486  2712 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 09:26:47.528  2486  2712 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 09:26:47.531  3555  3753 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 09:26:47.532  3555  3746 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 09:26:47.532  3555  3746 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 09:26:47.533  3555  3746 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@eb7458a
08-27 09:26:47.533  3555  3746 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 09:26:47.533  3555  3746 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 09:26:47.534  2486  2712 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 09:26:47.534  2486  2712 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 09:26:47.534  2486  2712 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 09:26:47.534  2486  2712 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 09:26:47.535   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 09:26:47.535  1349  2223 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3555:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 09:26:47.535  1349  2223 E ActivityManager: java.lang.Throwable
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 09:26:47.535  1349  2223 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 09:26:47.536   725   742 I mpu_uart: [TIME-:3914]:create
08-27 09:26:47.544   725   737 E mpu_uart: send_buff: 3c080011158000b0
08-27 09:26:47.545   725   737 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 09:26:47.548   725   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 09:26:47.548   725   742 I mpu_uart: [TIME-:3914]:delete
08-27 09:26:47.548   725   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 09:26:47.548   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 09:26:47.549  2486  2712 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 09:26:47.550  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 09:26:47.550   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:47.550  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 09:26:47.550   725   742 I mpu_uart: [TIME-:3915]:create
08-27 09:26:47.550  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 09:26:47.569   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:47.570   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 13:01:30.151  4768  4768 I (virq   : irq_count)- 3:411117 57:74000 217:43785 47:28876 216:12739 298:8715 281:7498 220:7172 10:4904 51:4794
01-01 13:01:30.151  4768  4768 I (cpu    : irq_count)- 0:275765 1:99654 2:115347 3:104801 4:5841 5:7898 6:8001 7:7807
01-01 13:01:30.151  4768  4768 I (ipi    : irq_count)- 0:469185 1:227694 2:0 3:0 4:0 5:200249 6:0
08-27 09:26:48.049  2486  3003 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 09:26:48.050  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 09:26:48.050  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 09:26:48.051   725   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:48.051   725   741 I mpu_uart: [TIME-:3916]:create
08-27 09:26:48.550   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 09:26:48.571   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 09:26:48.571   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:48.571   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 09:26:49.552   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 09:26:49.572   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 09:26:49.572   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:49.573   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
--------- beginning of system
08-27 09:26:49.645  1349  2005 I PowerGroup: Waking up power group from Dozing (groupId=0, uid=1000, reason=WAKE_REASON_POWER_BUTTON, details=android.policy:POWER)...
08-27 09:26:49.645  1349  1431 D CompatibilityChangeReporter: Compat change id reported: 173031413; UID 1000; state: ENABLED
08-27 09:26:49.646  1349  1431 D CompatibilityChangeReporter: Compat change id reported: 173031413; UID 1000; state: DISABLED
08-27 09:26:49.647  1349  2005 I PowerManagerService: Waking up from Dozing (uid=1000, reason=WAKE_REASON_POWER_BUTTON, details=android.policy:POWER)...
08-27 09:26:49.654  1349  1349 E ActivityManager: Cancel pending or running compactions as system is awake
08-27 09:26:49.658  1349  1514 I DisplayPowerController[0]: Blocking screen on until initial contents have been drawn.
08-27 09:26:49.668  1349  1514 V DisplayPowerController[0]: Brightness [0.48818898] reason changing to: 'manual', previous reason: 'screen_off'.
08-27 09:26:49.668  1349  1514 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.0, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 09:26:49.670  1349  1514 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.48818898, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 09:26:49.673  1006  1006 I QTI PowerHAL: Power setMode: 7 to: 1
08-27 09:26:49.677  1006  1006 I QTI PowerHAL: Got set_interactive hint
08-27 09:26:49.677  1116  1116 D SurfaceFlinger: Setting power mode 2 on display 4630946754821040514
08-27 09:26:49.678  1030  1090 I SDM     : DisplayBase::SetDisplayState: Set state = 1, display 54-0, teardown = 0
08-27 09:26:49.682  1349  1433 I DisplayDeviceRepository: Display device changed state: "内置屏幕", ON
01-01 13:01:32.263   380   380 I [drm:dsi_display_set_mode [msm_drm]] [msm-dsi-info]: mdp_transfer_time=0, hactive=240, vactive=320, fps=60
01-01 13:01:32.264   380   380 I [drm:dsi_ctrl_isr_configure [msm_drm]] [msm-dsi-info]: dsi-ctrl-0: IRQ 317 registered
08-27 09:26:49.688  1349  1433 I InputManager-JNI: Viewport [0] to add: local:4630946754821040514, isActive: true
01-01 13:01:32.269     0     0 I [    C2] ALERT: changing window size from 16000000 to 20000000 at 3452456137848
08-27 09:26:49.692  1008  1008 I sensors-hal: batch:240, android.sensor.accelerometer/11, period=200000000, max_latency=2000000000
08-27 09:26:49.692  1008  1008 I sensors-hal: set_config:64, sample_period_ns is adjusted to 200000000 based on min/max delay_ns
08-27 09:26:49.692  1008  1008 I sensors-hal: batch:249, android.sensor.accelerometer/11, period=200000000, max_latency=2000000000 request completed
08-27 09:26:49.694  1349  2005 I InputReader: Reconfiguring input devices, changes=DISPLAY_INFO | 
08-27 09:26:49.696  1349  2005 I InputReader: Device reconfigured: id=4, name='chsc_cap_touch', size 320x240, orientation 0, mode 1, display id 0
08-27 09:26:49.698  1349  1434 I DisplayDevice: [0] Layerstack set to 0 for local:4630946754821040514
08-27 09:26:49.699  1349  1492 E system_server: Cannot read thread CPU times for PID 1349
08-27 09:26:49.704  2271  2822 I BtGatt.ScanManager: msg.what = 7
08-27 09:26:49.706  2271  2822 D BtGatt.ScanManager: handleScreenOn()
08-27 09:26:49.710  1008  1008 I sensors-hal: activate:207, android.sensor.accelerometer/11 en=1
08-27 09:26:49.710  3480  3480 D NfcService: MSG_APPLY_SCREEN_STATE 8
08-27 09:26:49.710  1008  1008 I sensors-hal: get_qmi_debug_flag:241, support_qmi_debug : false
08-27 09:26:49.712   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fa went down
08-27 09:26:49.715  3480  3480 D NfcService: applyRouting enter
08-27 09:26:49.716  3480  3480 D NfcService: Discovery configuration equal, not updating.
08-27 09:26:49.716  3480  3480 I libnfc_nci: [INFO:NativeNfcManager.cpp(2992)] nfcManager_doSetScreenState: state = 8 prevScreenState= 1, discovry_param = 1
08-27 09:26:49.717  3480  3480 I libnfc_nci: [INFO:nfa_dm_api.cc(236)] NFA_SetPowerSubStateForScreenState: state:0x8
08-27 09:26:49.717  3480  3480 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007aa11a0e10 39:41
08-27 09:26:49.717  3480  3784 I libnfc_nci: [INFO:nfa_sys_main.cc(96)] NFA got event 0x011A
08-27 09:26:49.718   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fb went down
08-27 09:26:49.718  3480  3784 I libnfc_nci: [INFO:nfa_dm_main.cc(153)] event: NFA_DM_API_SET_POWER_SUB_STATE_EVT (0x1a)
08-27 09:26:49.718  3480  3784 I libnfc_nci: [INFO:nfa_dm_act.cc(674)] nfa_dm_set_power_sub_state
08-27 09:26:49.718  3480  3784 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007b71166d40 40:41
08-27 09:26:49.718  3480  3784 I libnfc_nci: [INFO:nfc_ncif.cc(497)] nfc_ncif_send_cmd()
08-27 09:26:49.718  3480  3784 I libnfc_nci: [INFO:nfc_ncif.cc(145)] check_and_store_last_cmd:
08-27 09:26:49.718  3480  3784 I libnfc_nci: [INFO:NfcAdaptation.cc(931)] NfcAdaptation::HalWrite
08-27 09:26:49.719  1081  3796 D NxpTml  : PN54X - Write requested.....
08-27 09:26:49.719  1081  3796 D NxpTml  : PN54X - Invoking I2C Write.....
08-27 09:26:49.720  3519  3519 D SYS_EVENT: send event 64 notification to client
08-27 09:26:49.720  2163  2163 D CentralSurfaces: mShouldDelayWakeUpAnimation CLEARED
08-27 09:26:49.721  2163  2163 W OnBackInvokedCallback: OnBackInvokedCallback is not enabled for the application.
08-27 09:26:49.721  2163  2163 W OnBackInvokedCallback: Set 'android:enableOnBackInvokedCallback="true"' in the application manifest.
08-27 09:26:49.721  2163  2163 D SbStateController: setState: requested state SHADE!= upcomingState: KEYGUARD. This usually means the status bar state transition was interrupted before the upcoming state could be applied.
08-27 09:26:49.722  3610  3610 E SUI_svcsock: svc_sock_send_message(suisvc): invalid remote socket suilst
08-27 09:26:49.728   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fc went down
08-27 09:26:49.730  1349  1431 I Pointer : Device Changed: Input Device 4: chsc_cap_touch
08-27 09:26:49.730  1349  1431 I Pointer :   Descriptor: 524d36e220110f6ef21ee0aa368c054e5d318528
08-27 09:26:49.730  1349  1431 I Pointer :   Generation: 18
08-27 09:26:49.730  1349  1431 I Pointer :   Location: built-in
08-27 09:26:49.730  1349  1431 I Pointer :   Keyboard Type: none
08-27 09:26:49.730  1349  1431 I Pointer :   Has Vibrator: false
08-27 09:26:49.730  1349  1431 I Pointer :   Has Sensor: false
08-27 09:26:49.730  1349  1431 I Pointer :   Has battery: false
08-27 09:26:49.730  1349  1431 I Pointer :   Has mic: false
08-27 09:26:49.730  1349  1431 I Pointer :   Sources: 0x1002 ( touchscreen )
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_X: source=0x1002 min=0.0 max=319.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_Y: source=0x1002 min=0.0 max=239.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_PRESSURE: source=0x1002 min=0.0 max=1.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_SIZE: source=0x1002 min=0.0 max=1.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_TOUCH_MAJOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_TOUCH_MINOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_TOOL_MAJOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.730  1349  1431 I Pointer :     AXIS_TOOL_MINOR: source=0x1002 min=0.0 max=400.0 flat=0.0 fuzz=0.0 resolution=0.0
08-27 09:26:49.732  2163  2163 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 09:26:49.734  1349  1990 D ActivityTaskManager: Top Process State changed to PROCESS_STATE_TOP
08-27 09:26:49.735  1008  1008 I sensors-hal: send_sync_sensor_request:358, send sync request
08-27 09:26:49.735  1008  1008 I sensors-hal: send_sync_sensor_request:384, wait for notification of response
08-27 09:26:49.735   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41fd went down
08-27 09:26:49.737  1008  4860 I sensors-hal: ssc_conn_resp_cb:663, resp_value = 0
08-27 09:26:49.737  1008  1008 I sensors-hal: send_sync_sensor_request:390, takes 1 ms to receive the response with 0
08-27 09:26:49.737  1008  1008 I sensors-hal: activate:218, android.sensor.accelerometer/11 en=1 completed
08-27 09:26:49.738  2163  2163 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 09:26:49.745  1081  3796 D NxpTml  : Write errno : 6b
08-27 09:26:49.745  1081  3796 D NxpTml  : PN54X - Error in I2C Write.....
08-27 09:26:49.745  1081  3796 D NxpTml  : PN54X - Posting Fresh Write message.....
08-27 09:26:49.745  1081  3796 D NxpTml  : PN54X - Tml Writer Thread Running................
08-27 09:26:49.745  1081  3798 D NxpHal  : write error status = 0x1ff
08-27 09:26:49.745  1081  1081 D NxpHal  : write_unlocked failed - PN54X Maybe in Standby Mode - Retry
01-01 13:01:32.302  3796  3796 E i2c_geni a84000.i2c: i2c error :-107
01-01 13:01:32.323  3796  3796 W i2c_write: write failed ret(-107), maybe in standby
08-27 09:26:49.749  2163  2163 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 09:26:49.750  1349  1349 D CompatibilityInfo: mCompatibilityFlags - 4
08-27 09:26:49.750  1349  1349 D CompatibilityInfo: applicationDensity - 160
08-27 09:26:49.750  1349  1349 D CompatibilityInfo: applicationScale - 1.0
08-27 09:26:49.754  2163  2163 D CentralSurfaces: Received new disable state: enaihbcrso.qingr (unchanged)
08-27 09:26:49.755  1081  3796 D NxpTml  : PN54X - Write requested.....
08-27 09:26:49.755  1081  3796 D NxpTml  : PN54X - Invoking I2C Write.....
08-27 09:26:49.756  1081  3796 D NxpNciX : len =   4 > 20090100
08-27 09:26:49.756  1081  3796 D NxpTml  : PN54X - I2C Write successful.....
08-27 09:26:49.756  1081  3796 D NxpTml  : PN54X - Posting Fresh Write message.....
08-27 09:26:49.756  1081  3796 D NxpTml  : PN54X - Tml Writer Thread Running................
08-27 09:26:49.756  1081  3798 D NxpHal  : write successful status = 0x0
08-27 09:26:49.758  1081  3795 D NxpTml  : PN54X - I2C Read successful.....
08-27 09:26:49.758  1081  3795 D NxpNciR : len =   4 > 40090100
08-27 09:26:49.758  1081  3795 D NxpTml  : PN54X - Posting read message.....
08-27 09:26:49.758  1081  3798 D NxpHal  : read successful status = 0x0
08-27 09:26:49.760  3480  3735 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007b71166d40 39:41
08-27 09:26:49.761  1081  3795 D NxpTml  : PN54X - Read requested.....
08-27 09:26:49.761  1081  3795 D NxpTml  : PN54X - Invoking I2C Read.....
08-27 09:26:49.762  3480  3784 I libnfc_nci: [INFO:nfc_ncif.cc(583)] NFC received rsp gid:0
08-27 09:26:49.763  3480  3784 I libnfc_nci: [INFO:nci_hrcv.cc(87)] nci_proc_core_rsp opcode:0x9
08-27 09:26:49.764  2163  2163 D CentralSurfaces: updateQsExpansionEnabled - QS Expand enabled: true
08-27 09:26:49.764  3480  3784 I libnfc_nci: [INFO:nfa_dm_act.cc(295)] unknown revt(0x5012)
08-27 09:26:49.766  3480  3784 I libnfc_nci: [INFO:NativeNfcManager.cpp(1027)] nfaDeviceManagementCallback: enter; event=0xB
08-27 09:26:49.766  3480  3784 I libnfc_nci: [INFO:NativeNfcManager.cpp(1231)] nfaDeviceManagementCallback: NFA_DM_SET_POWER_SUB_STATE_EVT; status=0x0
08-27 09:26:49.767  3480  3480 I libnfc_nci: [INFO:nfa_dm_api.cc(294)] param_id:0x2
08-27 09:26:49.768  2458  3287 D MonitorTool_MemoryControlService: [08-27 09:26:49.767] [pool-2-thread-1:24] Starting storage space check, time since last check: 30 seconds
08-27 09:26:49.768  3480  3480 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007ab11a56e0 39:41
08-27 09:26:49.768  2458  3287 V MonitorTool_MemoryControlService: [08-27 09:26:49.768] [pool-2-thread-1:24] On-demand mode: SD card check will be performed only when cleaning is needed
08-27 09:26:49.768  2458  3287 D MonitorTool_MemoryControlService: [08-27 09:26:49.768] [pool-2-thread-1:24] On-demand mode: Skipping SD card detection for storage space check
08-27 09:26:49.768  2458  3287 W MonitorTool_MemoryControlService: [08-27 09:26:49.768] [pool-2-thread-1:24] Attempting to use external storage path as fallback
08-27 09:26:49.769  3480  3784 I libnfc_nci: [INFO:nfa_sys_main.cc(96)] NFA got event 0x0102
08-27 09:26:49.770  3480  3784 I libnfc_nci: [INFO:nfa_dm_main.cc(153)] event: NFA_DM_API_SET_CONFIG_EVT (0x02)
08-27 09:26:49.770   893  3477 D audio_hw_primary: adev_set_parameters: enter: screen_state=on
08-27 09:26:49.771   893  3477 D sound_trigger_hw: handle_screen_status_change: screen on
08-27 09:26:49.771  2798  3505 E OpenGLRenderer: Unable to match the desired swap behavior.
08-27 09:26:49.772  3480  3784 I libnfc_nci: [INFO:nfa_dm_main.cc(257)] nfa_dm_check_set_config
08-27 09:26:49.772  3480  3784 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007b71166d40 40:41
08-27 09:26:49.773  2458  3287 D MonitorTool_MemoryControlService: [08-27 09:26:49.772] [pool-2-thread-1:24] Using external storage path: /storage/emulated/0
08-27 09:26:49.773  3480  3784 I libnfc_nci: [INFO:nfc_ncif.cc(497)] nfc_ncif_send_cmd()
08-27 09:26:49.774  3480  3784 I libnfc_nci: [INFO:nfc_ncif.cc(145)] check_and_store_last_cmd:
08-27 09:26:49.774  3480  3784 I libnfc_nci: [INFO:NfcAdaptation.cc(931)] NfcAdaptation::HalWrite
08-27 09:26:49.774  1349  1349 I SideFpsEventHandler: notifyPowerPressed
08-27 09:26:49.775  1081  3796 D NxpTml  : PN54X - Write requested.....
08-27 09:26:49.775  1081  3796 D NxpTml  : PN54X - Invoking I2C Write.....
08-27 09:26:49.775  1349  1349 I FingerprintManager: onPowerPressed
08-27 09:26:49.775  1349  1349 I WindowManager: Suppressed redundant power key press while already in the process of turning the screen on.
08-27 09:26:49.775  1081  3796 D NxpNciX : len =   7 > 20020401020101
08-27 09:26:49.776  1081  3796 D NxpTml  : PN54X - I2C Write successful.....
08-27 09:26:49.776  1081  3796 D NxpTml  : PN54X - Posting Fresh Write message.....
08-27 09:26:49.776  1081  3796 D NxpTml  : PN54X - Tml Writer Thread Running................
08-27 09:26:49.776  1349  1349 E Fingerprint21: onPowerPressed not supported for HIDL clients
08-27 09:26:49.777  1081  3795 D NxpTml  : PN54X - I2C Read successful.....
08-27 09:26:49.777  1081  3795 D NxpNciR : len =   5 > 4002020000
08-27 09:26:49.777  1081  3795 D NxpTml  : PN54X - Posting read message.....
08-27 09:26:49.777  1081  3798 D NxpHal  : write successful status = 0x0
08-27 09:26:49.777  1081  3798 D NxpHal  : read successful status = 0x0
08-27 09:26:49.777  1081  3798 D NxpHal  : phNxpNciHal_print_res_status: response status =STATUS_OK
08-27 09:26:49.780  1349  1349 D FastPairService: onReceive: ACTION_SCREEN_ON or boot complete.
08-27 09:26:49.780  1349  1349 V FastPairService: invalidateScan: scan is disabled
08-27 09:26:49.779  3480  3735 I libnfc_nci: [INFO:gki_buffer.cc(328)] GKI_getbuf 0xb400007b71168640 41:41
08-27 09:26:49.780  1349  1349 E NearbyManager: Cannot stop scan with this callback because it is never registered.
08-27 09:26:49.782  1081  3795 D NxpTml  : PN54X - Read requested.....
08-27 09:26:49.782  1081  3795 D NxpTml  : PN54X - Invoking I2C Read.....
08-27 09:26:49.782  2458  3287 V MonitorTool_MemoryControlService: [08-27 09:26:49.781] [pool-2-thread-1:24] Storage space details: Available blocks=8700712, Block size=4096 bytes, Total available=35638116352 bytes
08-27 09:26:49.783  3480  3784 I libnfc_nci: [INFO:nfc_ncif.cc(583)] NFC received rsp gid:0
08-27 09:26:49.783  3480  3784 I libnfc_nci: [INFO:nci_hrcv.cc(87)] nci_proc_core_rsp opcode:0x2
08-27 09:26:49.784  2458  3287 I MonitorTool_MemoryControlService: [08-27 09:26:49.784] [pool-2-thread-1:24] Current available storage space: 33987MB, minimum threshold: 500MB
08-27 09:26:49.784  2458  3287 D MonitorTool_MemoryControlService: [08-27 09:26:49.784] [pool-2-thread-1:24] Storage space sufficient, no cleaning needed
08-27 09:26:49.784  3480  3784 I libnfc_nci: [INFO:nfa_dm_act.cc(295)] NFC_SET_CONFIG_REVT(0x5002)
08-27 09:26:49.786  3480  3784 I libnfc_nci: [INFO:NativeNfcManager.cpp(1027)] nfaDeviceManagementCallback: enter; event=0x2
08-27 09:26:49.787  3480  3784 I libnfc_nci: [INFO:NativeNfcManager.cpp(1056)] nfaDeviceManagementCallback: NFA_DM_SET_CONFIG_EVT
08-27 09:26:49.789  3555  3789 I EventProvider: [EventProviderApp] handleIntent: action = android.intent.action.SCREEN_ON
08-27 09:26:49.791  3555  3789 D EventProvider: [EventProviderApp] LedEventListener: onEventReceived begin
08-27 09:26:49.793  3555  3789 W EventProvider: [EventProviderApp] LedEventListener: onEventReceived value = 0
08-27 09:26:49.794  3555  3789 I TcLightsManager: handleEvent: eventType = 11
08-27 09:26:49.795  3555  3789 I EventProvider: [EventProviderApp] LedEventListener: onEventReceived finished !
08-27 09:26:49.795  3555  3789 I EventProvider: [EventProviderApp] response wakeup
08-27 09:26:49.798  1349  2223 E ActivityManager: Sending non-protected broadcast yellowstone.system.SYS_WAKEUP from system 3555:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 09:26:49.798  1349  2223 E ActivityManager: java.lang.Throwable
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 09:26:49.798  1349  2223 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 09:26:49.801  2486  2563 D McuOtaServiceApp_EventBrokerManager: onEventReceived: Event received - power_save_state
08-27 09:26:49.802  2486  2563 I McuOtaServiceApp_EventBrokerManager: onEventReceived: categoryId: 2, typeId: 10
08-27 09:26:49.803  2486  3761 I McuOtaServiceApp_EventBrokerManager: handleEventBrokerEvent: POWER_SAVE event received
08-27 09:26:49.804  2486  3761 I McuOtaServiceApp_EventBrokerManager: handleEventBrokerEvent: POWER_SAVE exit
08-27 09:26:49.804  3555  3789 D EventProvider: [EventProviderApp] EventBrokerManager: JSON event published successfully - power_save_state
08-27 09:26:49.805  3555  3789 I EventProvider: [EventProviderApp] writeGpioActive: start
08-27 09:26:49.805  3555  3789 I EventProvider: [EventProviderApp] writeGpioFile: filePath=/sys/devices/platform/soc/soc:gpio_info/qcm_info_mcu2_gpio79, target=1
08-27 09:26:49.811  1349  1989 D ActivityManager: sync unfroze 3812 com.qualcomm.qti.biometrics.fingerprint.service:remote
08-27 09:26:49.813  3812  3812 D qfp-service: screen on
08-27 09:26:49.817  1823  1823 I QFP     : notifyPowerState: entry
08-27 09:26:49.817  1823  1823 I QFP     : getPersistPropVal: persist.vendor.qfp = true (default false)
08-27 09:26:49.817  1823  1823 I QFP     : getPersistPropVal: persist.vendor.qfp.wup_display = 0 (default 0)
08-27 09:26:49.817  1823  1823 I QFP     : notifyPowerState: wake isEnableFD=true
08-27 09:26:49.817  1823  1823 I QFP     : Initializing QFP framework, container 0 handle 0
08-27 09:26:49.817  1823  1823 I QFP     : getPersistPropVal: persist.vendor.qfp.qbt_fd_name = /dev/qbt_fd (default /dev/qbt_fd)
08-27 09:26:49.817  1823  1823 E QFP     : openSensor: failed to open /dev/qbt_fd
08-27 09:26:49.817  1823  1823 E QFP     : qfp_init failed -1
08-27 09:26:49.823  2327  2327 D DeviceStatisticsService: Send device is interactive
08-27 09:26:49.825  3555  3789 I EventProvider: [EventProviderApp] writeGpioActive: result79=true
08-27 09:26:49.826  3610  3610 E SUI_svcsock: svc_sock_send_message(suisvc): invalid remote socket suilst
08-27 09:26:49.828  3519  3519 D SYS_EVENT: send event 16 notification to client
08-27 09:26:49.836  3512  3512 D PASRDozeReceiver: Received Intent Intent { act=android.intent.action.SCREEN_ON flg=0x50200010 }
08-27 09:26:49.842  3512  3512 D PASRDozeReceiver: Sent intent Successfully
08-27 09:26:49.842  3512  3512 D PASRService: PASRService started
08-27 09:26:49.843  3512  3512 D PASRService: OnStartteett, Intent: android.intent.action.SCREEN_ON
08-27 09:26:49.843  3512  3512 D PASRService: Screen Off timer prop: 0. val: 0
08-27 09:26:49.849  3512  3512 I android_os_HwBinder: HwBinder: Starting thread pool for getting: vendor.qti.memory.pasrmanager@1.0::IPasrManager/pasrhal
08-27 09:26:49.852  3512  3512 D PASRService: is ActiveMode Enabled: false
08-27 09:26:49.853  3512  3512 D PASRService: Attempting Online All
08-27 09:26:49.853  3512  3512 D PASRService: State is now: -10
08-27 09:26:49.857  1349  1514 I DisplayPowerController[0]: Unblocked screen on after 199 ms
08-27 09:26:49.858  1008  1008 I sensors-hal: batch:240, android.sensor.device_orientation/272, period=66667000, max_latency=0
08-27 09:26:49.858  1008  1008 I sensors-hal: set_config:69, no need to adjust sample_period_ns due to on_change_sp_enabled = 1 or reporting_mode = 2
08-27 09:26:49.858  1008  1008 I sensors-hal: batch:249, android.sensor.device_orientation/272, period=66667000, max_latency=0 request completed
08-27 09:26:49.858  1008  1008 I sensors-hal: activate:207, android.sensor.device_orientation/272 en=1
08-27 09:26:49.858  1008  1008 I sensors-hal: get_qmi_debug_flag:241, support_qmi_debug : false
08-27 09:26:49.860   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 41ff went down
08-27 09:26:49.861  2163  2163 D CentralSurfaces: Received new disable state: enaihbcrso.qingr (unchanged)
08-27 09:26:49.862  1349  1514 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.48818898, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 09:26:49.864   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4200 went down
08-27 09:26:49.866   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4201 went down
08-27 09:26:49.869   892   945 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4202 went down
08-27 09:26:49.869  1008  1008 I sensors-hal: send_sensor_config_request:481, dt=device_orient: convert sample_period=66667000 to batch_period=66667
08-27 09:26:49.869  1008  1008 I sensors-hal: send_sync_sensor_request:358, send sync request
08-27 09:26:49.869  1008  1008 I sensors-hal: send_sync_sensor_request:384, wait for notification of response
08-27 09:26:49.870  1008  4867 I sensors-hal: ssc_conn_resp_cb:663, resp_value = 0
08-27 09:26:49.871  1008  1008 I sensors-hal: send_sync_sensor_request:390, takes 1 ms to receive the response with 0
08-27 09:26:49.871  1008  1008 I sensors-hal: activate:218, android.sensor.device_orientation/272 en=1 completed
08-27 09:26:49.875  1008  4860 I sensors-hal: handle_indication_realtime:471,  SCHED_FIFO(10) for qmi_cbk
08-27 09:26:49.898  2163  2344 D ControlsListingControllerImpl: Unsubscribing callback
08-27 09:26:49.900  2163  2344 D ControlsListingControllerImpl: Unsubscribing callback
08-27 09:26:49.902  2163  2344 D ControlsListingControllerImpl: Unsubscribing callback
08-27 09:26:50.003  1030  1090 I SDM     : DisplayBase::SetDisplayState: active 1-1 state 1-1 pending_power_state_ 0
08-27 09:26:50.003  1116  1116 D DisplayDevice: getVsyncPeriodFromHWC: Called HWC getDisplayVsyncPeriod. No error. period=16666666
08-27 09:26:50.003  1116  1116 D SurfaceFlinger: Finished setting power mode 2 on display 4630946754821040514
08-27 09:26:50.006  1349  2119 D SurfaceControl: Excessive delay in setPowerMode()
08-27 09:26:50.007  1349  2119 E DisplayDeviceConfig: requesting nits when no mapping exists.
08-27 09:26:50.007  1349  1433 I DisplayDeviceRepository: Display device changed state: "内置屏幕", ON
08-27 09:26:50.014  1030  1090 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:50.015  1349  1514 I DisplayPowerController[0]: BrightnessEvent: disp=0, physDisp=local:4630946754821040514, brt=0.48818898, initBrt=0.48818898, rcmdBrt=NaN, preBrt=NaN, lux=0.0, preLux=0.0, hbmMax=1.0, hbmMode=off, rbcStrength=50, powerFactor=1.0, thrmMax=1.0, wasShortTermModelActive=false, flags=, reason=manual, autoBrightness=false
08-27 09:26:50.018  1349  1514 I LatencyTracker: ACTION_TURN_ON_SCREEN latency=372
08-27 09:26:50.019  1349  1514 W PowerManagerService: Screen on took 383 ms
08-27 09:26:50.028  1349  2119 E DisplayDeviceConfig: requesting nits when no mapping exists.
08-27 09:26:50.030  1349  1514 I DreamManagerService: Gently waking up from dream.
01-01 13:01:32.600   380   380 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:32.600   380   380 I dsi_panel_set_backlight: start to update st7789vi lcd backlight, bl_lvl=0, bl_max_level=255, duty=0%
01-01 13:01:32.600   380   380 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:32.600   380   380 F         : [CHSC] function = semi_touch_reset              , line = 14  : set status before reset tp...
01-01 13:01:32.610   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:50.032  2271  2822 I BtGatt.ScanManager: msg.what = 7
01-01 13:01:32.721   380   380 F         : [CHSC] function = semi_touch_reset_and_detect   , line = 463 : set status pointing...
01-01 13:01:32.731   380   380 F         : [CHSC] function = semi_touch_resume_entry       , line = 625 : tpd_resume...
08-27 09:26:50.173  1030  1090 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:32.752  1260  1260 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:32.752  1260  1260 I dsi_panel_set_backlight: start to update st7789vi lcd backlight, bl_lvl=124, bl_max_level=255, duty=48%
01-01 13:01:32.752  1260  1260 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:50.192  1349  1990 I DreamManagerService: Leaving dreamland.
08-27 09:26:50.193  1349  1430 I DreamController: Stopping dream: name=ComponentInfo{com.android.systemui/com.android.systemui.doze.DozeService}, isPreviewMode=false, canDoze=true, userId=0, reason='finished self'(from 'power manager request')
08-27 09:26:50.218  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:50.242  2163  2163 D SbStateController: setIsDreaming:false
08-27 09:26:50.251  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:50.267  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:50.386  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:50.485   725   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 09:26:50.485   725   742 I mpu_uart: [TIME-:3915]:delete
08-27 09:26:50.485   725   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x35, 0x31, 0x37, 0x30, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x1a, ]
08-27 09:26:50.485   725   738 V mpu_uart: mcu_info:s_log_print_cnt=5170,current_state=0, ota_state = 1
08-27 09:26:50.485   725   738 V mpu_uart:  >> log: 
08-27 09:26:50.500   725   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 09:26:50.501   725   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x35, 0x38, 0x20, 0x6d, 0x76, 0xa, 0x79, ]
08-27 09:26:50.501   725   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12358 mv
08-27 09:26:50.501   725   738 V mpu_uart:  >> log: 
08-27 09:26:50.505   725   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 09:26:50.505   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 09:26:50.514   725   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 09:26:50.514   725   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 09:26:50.514   725   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 09:26:50.514   725   738 V mpu_uart:  >> log: 
08-27 09:26:50.514   725   741 I mpu_uart: [TIME-:3916]:delete
08-27 09:26:50.515  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 09:26:50.516  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 09:26:50.517   725   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:50.517   725   741 I mpu_uart: [TIME-:3917]:create
08-27 09:26:50.524   725   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 09:26:50.524   725   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 09:26:50.524   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:50.526  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 09:26:50.526  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 09:26:50.526  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 09:26:50.534   725   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 09:26:50.535   725   737 E mpu_uart: send_buff: 3c0700111491af
08-27 09:26:50.535   725   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x46, 0xdd, ]
08-27 09:26:50.535   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 09:26:50.535   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:50.537  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x46 
08-27 09:26:50.537  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 09:26:50.538  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 09:26:50.538  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12358
08-27 09:26:50.538  2486  2704 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12358
08-27 09:26:50.546   725   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 09:26:50.546   725   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 09:26:50.546   725   741 I mpu_uart: [TIME-:3917]:delete
08-27 09:26:50.546   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:50.547  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 09:26:50.547  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 09:26:50.548  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 09:26:50.548  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 09:26:50.549  2486  2704 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 09:26:50.550  2486  2704 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 09:26:50.551  2486  2704 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 09:26:51.027  2486  2712 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 09:26:51.028  2486  2712 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 09:26:51.030  3555  3753 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 09:26:51.033  2486  2712 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 09:26:51.033  2486  2712 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 09:26:51.034  2486  2712 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 09:26:51.034  2486  2712 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 09:26:51.034  3555  3746 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 09:26:51.034  3555  3746 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 09:26:51.035  3555  3746 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@230a018
08-27 09:26:51.035   725   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 09:26:51.035   725   741 I mpu_uart: [TIME-:3918]:create
08-27 09:26:51.035  3555  3746 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 09:26:51.035  3555  3746 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 09:26:51.037   725   737 E mpu_uart: send_buff: 3c080011158000b0
08-27 09:26:51.037   725   737 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 09:26:51.038  1349  1990 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3555:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 09:26:51.038  1349  1990 E ActivityManager: java.lang.Throwable
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 09:26:51.038  1349  1990 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 09:26:51.040   725   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 09:26:51.040   725   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 09:26:51.041   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 09:26:51.041   725   741 I mpu_uart: [TIME-:3918]:delete
08-27 09:26:51.043  2486  2712 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 09:26:51.043  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 09:26:51.043   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:51.043  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 09:26:51.043   725   742 I mpu_uart: [TIME-:3919]:create
08-27 09:26:51.043  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 09:26:51.062   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:51.063   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 13:01:34.015   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.023   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.032   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.041   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.050   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.059   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.068   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.493  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.077   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.086   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.095   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.104   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.525  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.114   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.542  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.122   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.547  2486  3003 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 09:26:51.548  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 09:26:51.548  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 09:26:51.549   725   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:51.549   725   741 I mpu_uart: [TIME-:3920]:create
01-01 13:01:34.131   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.559  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.140   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.150   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.159   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.588  2458  3906 D MonitorTool_MonitorAppService: [08-27 09:26:51.587] [pool-3-thread-1:35] Starting to check status of 3 apps
01-01 13:01:34.168   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.590  2458  3906 V MonitorTool_MonitorAppService: [08-27 09:26:51.589] [pool-3-thread-1:35] App[drvsys] not detected running
08-27 09:26:51.590  2458  3906 V MonitorTool_MonitorAppService: [08-27 09:26:51.590] [pool-3-thread-1:35] App[drvsys] running status: Not running
08-27 09:26:51.590  2458  3906 W MonitorTool_MonitorAppService: [08-27 09:26:51.590] [pool-3-thread-1:35] App[drvsys] not running, attempting to start
08-27 09:26:51.591  2458  3906 E MonitorTool_MonitorAppService: [08-27 09:26:51.591] [pool-3-thread-1:35] Could not get launch intent for app[drvsys], it might not be a launchable app
08-27 09:26:51.592  2458  3906 V MonitorTool_MonitorAppService: [08-27 09:26:51.592] [pool-3-thread-1:35] App[com.thundercomm.testapp] not detected running
08-27 09:26:51.593  2458  3906 V MonitorTool_MonitorAppService: [08-27 09:26:51.593] [pool-3-thread-1:35] App[com.thundercomm.testapp] running status: Not running
08-27 09:26:51.593  2458  3906 W MonitorTool_MonitorAppService: [08-27 09:26:51.593] [pool-3-thread-1:35] App[com.thundercomm.testapp] not running, attempting to start
08-27 09:26:51.594  2458  3906 E MonitorTool_MonitorAppService: [08-27 09:26:51.594] [pool-3-thread-1:35] Could not get launch intent for app[com.thundercomm.testapp], it might not be a launchable app
08-27 09:26:51.595  2458  3906 V MonitorTool_MonitorAppService: [08-27 09:26:51.595] [pool-3-thread-1:35] App[com.ssol.titanApp] not detected running
08-27 09:26:51.596  2458  3906 V MonitorTool_MonitorAppService: [08-27 09:26:51.595] [pool-3-thread-1:35] App[com.ssol.titanApp] running status: Not running
08-27 09:26:51.596  2458  3906 W MonitorTool_MonitorAppService: [08-27 09:26:51.596] [pool-3-thread-1:35] App[com.ssol.titanApp] not running, attempting to start
01-01 13:01:34.177   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.597  2458  3906 E MonitorTool_MonitorAppService: [08-27 09:26:51.597] [pool-3-thread-1:35] Could not get launch intent for app[com.ssol.titanApp], it might not be a launchable app
01-01 13:01:34.186   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.611  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.195   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.204   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.213   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.222   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.645  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.231   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.240   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.249   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.678  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.258   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.267   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.695  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.276   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.285   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.712  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.294   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.303   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.730  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.312   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.321   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.330   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.339   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.764  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.348   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.781  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.357   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.366   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.375   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.798  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.384   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.815  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.393   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.402   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.411   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.421   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.848  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.429   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.439   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.865  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.447   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.456   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.882  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.465   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.475   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.900  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.484   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.493   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.916  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.502   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.511   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.934  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.520   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.951  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.529   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.538   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.547   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.556   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:51.984  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.565   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.574   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.001  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.583   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.592   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.018  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.601   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.610   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.035  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.619   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.041   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-01 13:01:34.628   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.052  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.637   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.063   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 09:26:52.063   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:52.063   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 13:01:34.646   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.655   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.664   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.087  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.673   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.103  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.682   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.691   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.700   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.120  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.709   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.137  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.718   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.727   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.154  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.736   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.746   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.171  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.755   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.764   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.773   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.782   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.205  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.791   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.800   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.809   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.818   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.827   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.256  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.836   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.845   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.854   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.863   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.872   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.881   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.307  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.890   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.899   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.908   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.917   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.926   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.935   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.359  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.944   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.954   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.377  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.963   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.972   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.981   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:34.984  4759  4759 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
08-27 09:26:52.403  1066  1066 D vendor.qti.vibrator: Vibrator on for timeoutMs: 20
08-27 09:26:52.405  1066  4875 D vendor.qti.vibrator: Starting on on another thread
08-27 09:26:52.410  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:34.996  4759  4759 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
08-27 09:26:52.422   893  4718 D audio_hw_primary: start_output_stream: enter: stream(0xe4c821a0)usecase(1: low-latency-playback) devices(0x2) is_haptic_usecase(0)
01-01 13:01:34.999   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.422   893  4718 D audio_hw_primary: select_devices for use case (low-latency-playback)
08-27 09:26:52.422   893  4718 I msm8974_platform: platform_check_and_set_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-27 09:26:52.422   893  4718 I msm8974_platform: platform_check_and_set_codec_backend_cfg: new_snd_devices[0] is 2
08-27 09:26:52.422   893  4718 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
01-01 13:01:35.008   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.425  1066  4875 D vendor.qti.vibrator: Notifying on complete
08-27 09:26:52.426   893  4718 D msm8974_platform: platform_check_codec_backend_cfg:becf: updated afe: bitwidth 16, samplerate 48000 channels 2,backend_idx 0 usecase = 1 device (speaker)
08-27 09:26:52.426   893  4718 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: Codec selected backend: 0 updated bit width: 16 and sample rate: 48000
08-27 09:26:52.426   893  4718 D audio_hw_primary: check_usecases_codec_backend:becf: force routing 0
08-27 09:26:52.426   893  4718 D audio_hw_primary: check_usecases_codec_backend:becf: (93) check_usecases curr device: speaker, usecase device: backends match 0
08-27 09:26:52.426   893  4718 D audio_hw_primary: check_usecases_codec_backend:becf: check_usecases num.of Usecases to switch 0
08-27 09:26:52.426   893  4718 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 09:26:52.426   893  4718 D audio_hw_primary: enable_snd_device: snd_device(2: speaker)
08-27 09:26:52.427   893  4718 D msm8974_platform: platform_get_island_cfg_on_device:island cfg status on snd_device = (speaker 0)
08-27 09:26:52.427   893  4718 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 1, with Raise=0
08-27 09:26:52.427   893  4718 D audio_route: Apply path: speaker
08-27 09:26:52.427   893  4718 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 3, with Raise=0
08-27 09:26:52.427  1066  1066 D vendor.qti.vibrator: QTI Vibrator off
08-27 09:26:52.427   893  4718 D audio_hw_utils: audio_extn_utils_send_app_type_cfg: usecase->out_snd_device speaker
01-01 13:01:35.013  4759  4759 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
08-27 09:26:52.427   893  4718 I audio_hw_utils: send_app_type_cfg_for_device PLAYBACK app_type 69937, acdb_dev_id 15, sample_rate 48000, snd_device_be_idx 39
08-27 09:26:52.427  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.016  4718  4718 I [Awinic][1-0034]aw882xx_startup: playback enter
08-27 09:26:52.429   893  4718 D ACDB-LOADER: ACDB -> send_audio_cal, acdb_id = 15, path = 0, app id = 69937, sample rate = 48000, use_case = 0,buffer_idx_w_path =0, afe_sample_rate = 48000, cal_mode = 1, offset_index = 0
08-27 09:26:52.429   893  4718 D ACDB-LOADER: ACDB -> send_asm_topology
08-27 09:26:52.429   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TOPOLOGY_ID
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> send_adm_topology
01-01 13:01:35.017   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TOPOLOGY_ID
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> send_audtable
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE_SIZE
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> AUDIO_SET_AUDPROC_CAL cal_type[11] acdb_id[15] app_type[69937]
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> send_audvoltable
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE_SIZE
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE, vol index 0
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> AUDIO_SET_VOL_CAL cal type = 12
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE_SIZE
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> send_audstrmtable
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> audstrm_cal->cal_type.cal_data.cal_size = 20
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> send_afe_topology
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_TOPOLOGY_ID
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> GET_AFE_TOPOLOGY_ID for adcd_id 15, Topology Id 112fc
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> send_afe_cal
08-27 09:26:52.430   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE
08-27 09:26:52.431   893  4718 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 09:26:52.431   893  4718 D ACDB-LOADER: Error: ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE Returned = -19
08-27 09:26:52.431   893  4718 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE
08-27 09:26:52.431   893  4718 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 09:26:52.431   893  4718 D ACDB-LOADER: Error: ACDB AFE returned = -19
08-27 09:26:52.431   893  4718 D ACDB-LOADER: ACDB -> AUDIO_SET_AFE_CAL cal_type[16] acdb_id[15]
08-27 09:26:52.431   893  4718 D ACDB-LOADER: ACDB -> send_hw_delay : acdb_id = 15 path = 0
08-27 09:26:52.431   893  4718 D ACDB-LOADER: ACDB -> ACDB_AVSYNC_INFO: ACDB_CMD_GET_DEVICE_PROPERTY
08-27 09:26:52.431   893  4718 D audio_hw_primary: enable_audio_route: apply mixer and update path: low-latency-playback
08-27 09:26:52.431   893  4718 D audio_route: Apply path: low-latency-playback
08-27 09:26:52.433   893  4718 D audio_hw_primary: select_devices: done
08-27 09:26:52.434   893  4718 D msm8974_platform: platform_set_channel_map mixer_ctl_name:Playback Channel Map9
08-27 09:26:52.434   893  4718 D msm8974_platform: platform_set_channel_map: set mapping(1 2 0 0 0 0 0 0) for channel:2
01-01 13:01:35.020  4718  4718 E aw_cali_get_read_cali_re: channel:0 open /mnt/vendor/persist/factory/audio/aw_cali.bin failed!
01-01 13:01:35.026   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.031  4718  4718 I [Awinic][1-0034]aw882xx_dev_init_cali_re: read nvram cali failed, use default Re
08-27 09:26:52.452  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.035   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.044   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.053   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.481  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.062   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.069  4718  4718 E msm_adsp_init_mixer_ctl_adm_pp_event_queue: failed to get kctl.
01-01 13:01:35.071   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.500  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.080   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.080  4718  4718 I afe_get_cal_topology_id: port_id = 0x1006 acdb_id = 15 topology_id = 0x112fc cal_type_index=8 ret=0
01-01 13:01:35.081  4718  4718 E send_afe_cal_type: No cal sent for cal_index 0, port_id = 0x1006! ret -22
01-01 13:01:35.089   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.091  4718  4718 I afe_send_hw_delay: port_id 0x1006 rate 48000 delay_usec 474 status 0
01-01 13:01:35.096  4718  4718 I [Awinic][1-0034]aw882xx_mute: mute state=0
01-01 13:01:35.096  4718  4718 I [Awinic][1-0034]aw882xx_spin_set_record_val: do nothing
01-01 13:01:35.096  4718  4718 I [Awinic][1-0034]aw882xx_spin_set_record_val: set record spin val done
01-01 13:01:35.096   310   310 I [Awinic][1-0034]aw882xx_startup_work: enter
01-01 13:01:35.096   310   310 I [Awinic][1-0034]aw882xx_start_pa: enter
01-01 13:01:35.096   310   310 I [Awinic][1-0034]aw882xx_dev_reg_update: done
01-01 13:01:35.096   310   310 I [Awinic][1-0034]aw_dev_pwd: done
01-01 13:01:35.098   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.099  4718  4718 E q6asm_find_cal_by_buf_number: Can't find ASM Cal for cal_index 2 app_type 69937 buffer_number 8
01-01 13:01:35.107   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.110   310   310 I [Awinic][1-0034]aw_dev_mode1_pll_check: done
01-01 13:01:35.112   310   310 I [Awinic][1-0034]aw_dev_amppd: done
08-27 09:26:52.534  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:52.538   893  4718 D audio_hw_primary: start_output_stream: exit
01-01 13:01:35.115   310   310 I [Awinic][1-0034]aw_dev_sysst_check: done
01-01 13:01:35.116   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.119   310   310 I [Awinic][1-0034]aw_pid_2113_reg_force_set: needn't set reg value
01-01 13:01:35.120   310   310 I [Awinic][1-0034]aw_dev_uls_hmute: done
01-01 13:01:35.125   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.134   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.135   310   310 I [Awinic][1-0034]aw_dev_mute: done
01-01 13:01:35.136   310   310 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-01 13:01:35.136   310   310 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-01 13:01:35.136   310   310 I [Awinic][1-0034]aw882xx_monitor_start: enter
01-01 13:01:35.137   310   310 I [Awinic][1-0034]aw882xx_device_start: done
01-01 13:01:35.137   310   310 I [Awinic][1-0034]aw882xx_start_pa: start success
01-01 13:01:35.137   310   310 I [Awinic][1-0034]aw_monitor_get_voltage: chip voltage is 3957
01-01 13:01:35.138   310   310 I [Awinic][1-0034]aw_monitor_get_temperature: reg val is 0x001a
01-01 13:01:35.138   310   310 I [Awinic][1-0034]aw_monitor_get_temperature: chip temperature = 26
01-01 13:01:35.140   310   310 I [Awinic][1-0034]aw_monitor_set_gain: set reg val = 0x15, gain = 0x4
01-01 13:01:35.140   310   310 E [Awinic]aw_check_dsp_ready: rx topo id is 0x0
01-01 13:01:35.147   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.567  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.152   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.161   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.585  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.170   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.179   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.188   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.619  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.197   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.206   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.635  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.215   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.224   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.233   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.242   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.669  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.251   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.260   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.686  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.269   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.278   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.702  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.287   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.296   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.719  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.305   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.314   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.736  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.323   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.332   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.754  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.341   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.358   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.367   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.788  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.376   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.385   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.394   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.822  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.403   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.412   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.839  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.421   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.430   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.440   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.449   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.872  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.458   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.467   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.889  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.476   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.485   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.494   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.923  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.503   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.512   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.940  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.521   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.530   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.539   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.548   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:52.974  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.557   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.566   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.575   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.584   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.008  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.593   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.602   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.611   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.620   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.042   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 09:26:53.042  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.629   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.638   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.064   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 09:26:53.064   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:53.064   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 13:01:35.647   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.075  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.656   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.665   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.674   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.683   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.109  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.692   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.701   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.711   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.720   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.143  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.729   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.738   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.747   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.756   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.177  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.765   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.774   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.783   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.210  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.792   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.801   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.810   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.819   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.244  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.828   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.837   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.846   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.855   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.864   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.873   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.295  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.882   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.891   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.313  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.900   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.909   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.918   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.345  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.928   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.937   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.362  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.946   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.955   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.379  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.964   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.973   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.396  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:35.982   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:35.991   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.413  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.000   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.431  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.009   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.018   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.448  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.027   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.036   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.045   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.054   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.481  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.063   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.072   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.498  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.081   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.090   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.515  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.099   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.108   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.532  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.117   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.126   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.549  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.135   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.144   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.566  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.153   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.582  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.162   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.171   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.599  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.180   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.189   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.616  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.198   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.207   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.633  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.216   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.225   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.651  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.234   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.243   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.667  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.252   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.261   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.685  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.270   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.279  4759  4759 I sd 0    : 0:0:0: [sda] Synchronizing SCSI cache
01-01 13:01:36.279   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.702  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.289   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.298   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.719  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:53.728   893  4718 W audio_hw_primary: out_write: underrun(9) frames_by_time(48067) > out->last_fifo_frames_remaining(384)
01-01 13:01:36.307   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.735  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.316   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.325   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.752  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.334   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.343   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.769  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.352   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.361   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.786  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.370   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.379   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.803  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.388   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.820  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.397   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.406   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.415   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.837  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.424   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.433   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.854  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.442   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.872  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.451   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.460   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.469   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.478   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.905  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.487   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.496   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.922  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.505   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.514   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.939  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.523   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.532   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.956  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.541   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.972  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.550   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.559   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.985   725   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 09:26:53.985   725   742 I mpu_uart: [TIME-:3919]:delete
08-27 09:26:53.985   725   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x35, 0x31, 0x37, 0x31, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x1b, ]
08-27 09:26:53.985   725   738 V mpu_uart: mcu_info:s_log_print_cnt=5171,current_state=0, ota_state = 1
08-27 09:26:53.985   725   738 V mpu_uart:  >> log: 
01-01 13:01:36.568   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:53.989  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.000   725   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 09:26:54.001   725   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x32, 0x39, 0x20, 0x6d, 0x76, 0xa, 0x7f, ]
08-27 09:26:54.001   725   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12329 mv
08-27 09:26:54.001   725   738 V mpu_uart:  >> log: 
01-01 13:01:36.577   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.005   725   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 09:26:54.005   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 09:26:54.006  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.586   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.014   725   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 09:26:54.014   725   741 I mpu_uart: [TIME-:3920]:delete
08-27 09:26:54.014   725   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 09:26:54.014   725   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 09:26:54.014   725   738 V mpu_uart:  >> log: 
08-27 09:26:54.014  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 09:26:54.014  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 09:26:54.015   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:54.015   725   742 I mpu_uart: [TIME-:3921]:create
01-01 13:01:36.595   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.023  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.604   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.024   725   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 09:26:54.024   725   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 09:26:54.025   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:54.025  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 09:26:54.026  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 09:26:54.026  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 09:26:54.034   725   737 E mpu_uart: send_buff: 3c0700111491af
08-27 09:26:54.034   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 09:26:54.035   725   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 09:26:54.035   725   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x29, 0xb2, ]
08-27 09:26:54.035   725   742 I mpu_uart: [TIME-:3921]:delete
08-27 09:26:54.035   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:54.035  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x29 
08-27 09:26:54.036  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 09:26:54.036  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 09:26:54.036  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12329
08-27 09:26:54.036  2486  2704 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12329
01-01 13:01:36.614   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.040  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.622   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.045   725   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 09:26:54.045   725   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 09:26:54.045   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 09:26:54.046  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 09:26:54.049  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 09:26:54.049  2486  2704 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 09:26:54.049  2486  2704 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 09:26:54.050  2486  2704 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 09:26:54.050  2486  2704 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 09:26:54.051  2486  2704 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
01-01 13:01:36.631   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.057  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.057  1066  1066 D vendor.qti.vibrator: QTI Vibrator off
01-01 13:01:36.640   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.649   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.074  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.658   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.667   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.092  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.676   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.685   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.694   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.703   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.125  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.712   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.142  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.722   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.731   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.159  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.740   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.749   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.176  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.758   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.193  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 13:01:36.775   537   537 I get_disable_lcd_state: s_disable_lcd = 0
01-01 13:01:36.784   537   537 I get_disable_lcd_state: s_disable_lcd = 0
08-27 09:26:54.209  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.227  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.278  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.328  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.379  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.430  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.482  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.500  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.517  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.527  2486  2712 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 09:26:54.527  2486  2712 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 09:26:54.529  3555  3753 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 09:26:54.530  3555  3789 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 09:26:54.530  3555  3789 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 09:26:54.531  3555  3789 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@a0baa71
08-27 09:26:54.531  3555  3789 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 09:26:54.531  3555  3789 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 09:26:54.533  2486  2712 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 09:26:54.533  2486  2712 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 09:26:54.534  2486  2712 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 09:26:54.534  2486  2712 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 09:26:54.535   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 09:26:54.536   725   742 I mpu_uart: [TIME-:3922]:create
08-27 09:26:54.536  1349  1392 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3555:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 09:26:54.536  1349  1392 E ActivityManager: java.lang.Throwable
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 09:26:54.536  1349  1392 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 09:26:54.543   725   737 E mpu_uart: send_buff: 3c080011158000b0
08-27 09:26:54.543   725   737 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 09:26:54.546   725   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 09:26:54.546   725   742 I mpu_uart: [TIME-:3922]:delete
08-27 09:26:54.546   725   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 09:26:54.547   725   739 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 09:26:54.547  2486  2712 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 09:26:54.548  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 09:26:54.548   725   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:54.548   725   741 I mpu_uart: [TIME-:3923]:create
08-27 09:26:54.548  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 09:26:54.548  2486  2704 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 09:26:54.551  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.567   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:54.567   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 09:26:54.567  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.585  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.602  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.715  1030  1260 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 09:26:54.767   893  4718 W audio_hw_primary: out_write: underrun(10) frames_by_time(48076) > out->last_fifo_frames_remaining(384)
08-27 09:26:55.036  2486  3003 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 09:26:55.036  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 09:26:55.036  2486  3003 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 09:26:55.037   725   742 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 09:26:55.037   725   742 I mpu_uart: [TIME-:3924]:create
08-27 09:26:55.547   725   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 09:26:55.567   725   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 09:26:55.567   725   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 09:26:55.568   725   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 09:26:55.806   893  3477 D audio_hw_primary: out_standby: enter: stream (0xe4c821a0) usecase(1: low-latency-playback)
01-01 13:01:38.386  3477  3477 I [Awinic][1-0034]aw882xx_mute: mute state=1
01-01 13:01:38.386  3477  3477 I [Awinic][1-0034]aw882xx_monitor_stop: enter
01-01 13:01:38.387  3477  3477 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-01 13:01:38.388  3477  3477 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-01 13:01:38.389  3477  3477 I [Awinic][1-0034]aw_dev_uls_hmute: done
01-01 13:01:38.425  3477  3477 I [Awinic][1-0034]aw_dev_mute: done
01-01 13:01:38.428  3477  3477 I [Awinic][1-0034]aw_dev_amppd: done
01-01 13:01:38.429  3477  3477 I [Awinic][1-0034]aw_dev_pwd: done
01-01 13:01:38.429  3477  3477 I [Awinic][1-0034]aw882xx_device_stop: done
01-01 13:01:38.438  3477  3477 E msm_adsp_clean_mixer_ctl_adm_pp_event_queue: failed to get kctl.
01-01 13:01:38.451  3477  3477 I [Awinic][1-0034]aw882xx_shutdown: stream playback
08-27 09:26:55.915   893  3477 D audio_hw_primary: disable_audio_route: reset and update mixer path: low-latency-playback
08-27 09:26:55.918   893  3477 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 2, with Raise=0
08-27 09:26:55.919   893  3477 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 09:26:55.919   893  3477 D audio_hw_primary: disable_snd_device: snd_device(2: speaker)
08-27 09:26:55.920   893  3477 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 0, with Raise=0
