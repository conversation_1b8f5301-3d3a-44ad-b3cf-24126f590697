--------- beginning of main
08-27 08:31:22.067  2543  3009 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:31:22.067  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:31:22.067  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:31:22.068   731   747 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:22.068   731   747 I mpu_uart: [TIME-:115]:create
--------- beginning of kernel
01-02 03:44:21.968   580   580 I logd    : logdr: UID=2000 GID=2000 PID=4771 b tail=0 logMask=99 pid=0 start=0ns deadline=0ns
08-27 08:31:22.569   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:31:22.590   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:31:22.591   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:22.591   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:44:22.487   330   330 I (virq   : irq_count)- 3:89997 217:44230 47:19330 57:15117 298:8809 220:7526 300:6283 10:5085 263:4188 313:4141
01-02 03:44:22.487   330   330 I (cpu    : irq_count)- 0:110476 1:38280 2:20582 3:22000 4:6190 5:6693 6:6982 7:7550
01-02 03:44:22.487   330   330 I (ipi    : irq_count)- 0:167046 1:74361 2:0 3:0 4:0 5:56510 6:0
08-27 08:31:23.571   731   746 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:31:23.592   731   744 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:31:23.593   731   744 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:31:23.593   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:31:24.502   731   745 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:31:24.503   731   731 I mpu_uart: [TIME-:114]:delete
08-27 08:31:24.503   731   745 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x30, 0x34, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x2e, ]
08-27 08:31:24.503   731   745 V mpu_uart: mcu_info:s_log_print_cnt=20304,current_state=0, ota_state = 1
08-27 08:31:24.503   731   745 V mpu_uart:  >> log: 
08-27 08:31:24.516   731   745 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:31:24.517   731   745 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x34, 0x31, 0x35, 0x20, 0x6d, 0x76, 0xa, 0x77, ]
08-27 08:31:24.517   731   745 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12415 mv
08-27 08:31:24.517   731   745 V mpu_uart:  >> log: 
08-27 08:31:24.523   731   744 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:31:24.523   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:31:24.530   731   745 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:31:24.530   731   745 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:31:24.530   731   745 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:31:24.530   731   745 V mpu_uart:  >> log: 
08-27 08:31:24.531   731   747 I mpu_uart: [TIME-:115]:delete
08-27 08:31:24.532  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:31:24.532  2543  3009 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:31:24.534   731   731 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:31:24.534   731   731 I mpu_uart: [TIME-:116]:create
08-27 08:31:24.541   731   745 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:31:24.541   731   745 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:31:24.541   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:24.543  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:31:24.543  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:31:24.543  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:31:24.550   731   745 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:31:24.551   731   745 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x63, 0xf8, ]
08-27 08:31:24.551   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:24.551   731   744 E mpu_uart: send_buff: 3c0700111491af
08-27 08:31:24.552   731   744 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:31:24.552  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x63 
08-27 08:31:24.552  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:31:24.552  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:31:24.553  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12387
08-27 08:31:24.553  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12387
08-27 08:31:24.563   731   745 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:31:24.563   731   745 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 08:31:24.563   731   746 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:31:24.564   731   731 I mpu_uart: [TIME-:116]:delete
08-27 08:31:24.566  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 08:31:24.567  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:31:24.567  2543  2742 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:31:24.568  2543  2742 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 08:31:24.568  2543  2742 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 08:31:24.568  2543  2742 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 08:31:24.571  2543  2742 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
