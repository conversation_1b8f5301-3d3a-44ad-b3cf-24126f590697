--------- beginning of kernel
01-01 11:33:24.938  8175  8175 I sd 0    : 0:0:0: [sda] Synchronizing SCSI cache
--------- beginning of main
08-27 10:03:52.220   723   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 10:03:52.221   723   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x33, 0x36, 0x35, 0x39, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x10, ]
08-27 10:03:52.221   723   738 V mpu_uart: mcu_info:s_log_print_cnt=3659,current_state=0, ota_state = 1
08-27 10:03:52.221   723   738 V mpu_uart:  >> log: 
08-27 10:03:52.221   723   741 I mpu_uart: [TIME-:6446]:delete
08-27 10:03:52.236   723   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 10:03:52.237   723   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x38, 0x37, 0x20, 0x6d, 0x76, 0xa, 0x73, ]
08-27 10:03:52.237   723   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12387 mv
08-27 10:03:52.237   723   738 V mpu_uart:  >> log: 
08-27 10:03:52.241   723   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 10:03:52.241   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 10:03:52.249   723   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 10:03:52.249   723   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 10:03:52.249   723   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 10:03:52.249   723   738 V mpu_uart:  >> log: 
08-27 10:03:52.249   723   740 I mpu_uart: [TIME-:6447]:delete
08-27 10:03:52.250  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 10:03:52.251  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 10:03:52.251   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:52.252   723   740 I mpu_uart: [TIME-:6448]:create
08-27 10:03:52.259   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:03:52.260   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 10:03:52.260   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:52.261  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 10:03:52.262  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 10:03:52.263  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 10:03:52.270   723   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 10:03:52.270   723   737 E mpu_uart: send_buff: 3c0700111491af
08-27 10:03:52.270   723   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x63, 0xf8, ]
08-27 10:03:52.270   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 10:03:52.270   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:52.272  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x63 
08-27 10:03:52.272  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 10:03:52.273  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 10:03:52.273  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12387
08-27 10:03:52.274  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12387
08-27 10:03:52.280   723   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 10:03:52.281   723   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 10:03:52.281   723   740 I mpu_uart: [TIME-:6448]:delete
08-27 10:03:52.281   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:52.282  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 10:03:52.283  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 10:03:52.283  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 10:03:52.284  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 10:03:52.284  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
08-27 10:03:52.285  2555  2732 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 10:03:52.287  2555  2732 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 10:03:52.765  2555  2734 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 10:03:52.766  2555  2734 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 10:03:52.769  3666  3873 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 10:03:52.770  3666  3863 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 10:03:52.771  3666  3863 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 10:03:52.771  2555  2734 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 10:03:52.771  2555  2734 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 10:03:52.771  3666  3863 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@3a4c464
08-27 10:03:52.771  3666  3863 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 10:03:52.772  2555  2734 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 10:03:52.772  3666  3863 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 10:03:52.772  2555  2734 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 10:03:52.773   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 10:03:52.773   723   740 I mpu_uart: [TIME-:6449]:create
08-27 10:03:52.773  1032  3574 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3666:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 10:03:52.773  1032  3574 E ActivityManager: java.lang.Throwable
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 10:03:52.773  1032  3574 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 10:03:52.792   723   737 E mpu_uart: send_buff: 3c080011158000b0
08-27 10:03:52.793   723   737 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 10:03:52.796   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:03:52.796   723   740 I mpu_uart: [TIME-:6449]:delete
08-27 10:03:52.796   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 10:03:52.797   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 10:03:52.798  2555  2734 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 10:03:52.799   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:52.799   723   740 I mpu_uart: [TIME-:6450]:create
08-27 10:03:52.799  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 10:03:52.800  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 10:03:52.801  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 10:03:52.817   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:52.817   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:26.065  8747  8747 I (virq   : irq_count)- 3:3934613 300:615123 57:520220 263:140052 220:139887 217:44306 51:36360 47:32173 223:30719 103:27537
01-01 11:33:26.066  8747  8747 I (cpu    : irq_count)- 0:1861547 1:1000077 2:998316 3:1761941 4:9842 5:17640 6:20932 7:11720
01-01 11:33:26.066  8747  8747 I (ipi    : irq_count)- 0:6845385 1:2171604 2:0 3:0 4:0 5:448741 6:0
08-27 10:03:53.283  2555  3001 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 10:03:53.284  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 10:03:53.285  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 10:03:53.286   723   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:53.286   723   741 I mpu_uart: [TIME-:6451]:create
08-27 10:03:53.798   723   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 10:03:53.818   723   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 10:03:53.819   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:53.819   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:26.738   571   571 I logd    : logdr: UID=2000 GID=2000 PID=8861 b tail=0 logMask=99 pid=0 start=0ns deadline=0ns
08-27 10:03:54.799   723   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 10:03:54.819   723   737 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 10:03:54.820   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:54.820   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 10:03:54.973  6495  6520 D CCodecBufferChannel: [c2.android.opus.encoder#354] DEBUG: elapsed: n=4 [in=0 pipeline=0 out=0]
08-27 10:03:54.974  6495  6520 D PipelineWatcher: DEBUG: elapsed 0 / 4
08-27 10:03:55.117  1059  8556 I QC2Comp : [avcE_42] Stats: Pending(0) i/p-done(0) Works: Q: 2872/Done 2872|Work-Rate: Q(0.2/s Avg=1.6/s) Done(0.200/s Avg=1.613/s)| Stream: 10.00fps 77.7Kbps
08-27 10:03:55.117  1059  8556 I QC2Comp : Mem-usage:  [In-2D - 65 bufs 24.375 MB] [1D-44 - 17 bufs 3.984 MB]
08-27 10:03:55.117  1059  8556 I QC2Comp : Total Mem-usage: 28.359 MB
08-27 10:03:55.230  6495  6518 D BufferPoolAccessor2.0: bufferpool2 0xb4000078dd46bf08 : 5(19200 size) total buffers - 4(15360 size) used buffers - 221380/221385 (recycle/alloc) - 1089/442761 (fetch/transfer)
01-01 11:33:28.172   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.181   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.190   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.199   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.400  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.208   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.413  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.217   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.421  1059  8561 D BufferPoolAccessor2.0: bufferpool2 0xee4806e8 : 4(983040 size) total buffers - 4(983040 size) used buffers - 2860/2877 (recycle/alloc) - 38/2872 (fetch/transfer)
01-01 11:33:28.226   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.235   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.433  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.244   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.443  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.450  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.253   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.262   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.460  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.271   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.281   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.483  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.290   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.299   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.495  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.308   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.501  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:55.513  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.317   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.326   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.335   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.534  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.344   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.545  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.353   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.551  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.362   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.563  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.371   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.380   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.586  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.389   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.398   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.601  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.602  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.407   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.416   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.614  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.425   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.434   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.637  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.443   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.648  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.452   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.654  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.461   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.470   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.669  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.479   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.488   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.498   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.507   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.705  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.516   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.717  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.720   723   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 10:03:55.720   723   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x33, 0x36, 0x36, 0x30, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x1a, ]
08-27 10:03:55.720   723   738 V mpu_uart: mcu_info:s_log_print_cnt=3660,current_state=0, ota_state = 1
08-27 10:03:55.720   723   738 V mpu_uart:  >> log: 
08-27 10:03:55.721   723   740 I mpu_uart: [TIME-:6450]:delete
01-01 11:33:28.525   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.534   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.735   723   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 10:03:55.735   723   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x31, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x35, 0x38, 0x20, 0x6d, 0x76, 0xa, 0x70, ]
08-27 10:03:55.736   723   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=310 r/s,vbat=12358 mv
08-27 10:03:55.736   723   738 V mpu_uart:  >> log: 
01-01 11:33:28.543   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.741   723   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 10:03:55.741   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
01-01 11:33:28.552   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.749   723   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 10:03:55.749   723   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 10:03:55.749   723   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 10:03:55.749   723   738 V mpu_uart:  >> log: 
08-27 10:03:55.749   723   741 I mpu_uart: [TIME-:6451]:delete
08-27 10:03:55.751  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 10:03:55.751  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 10:03:55.752   723   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:55.752   723   741 I mpu_uart: [TIME-:6452]:create
08-27 10:03:55.756  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:55.759   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:03:55.759   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 10:03:55.759   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:55.769   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 10:03:55.770  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.772   723   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 10:03:55.772   723   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x46, 0xdd, ]
08-27 10:03:55.772   723   741 I mpu_uart: [TIME-:6452]:delete
01-01 11:33:28.579   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.772   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:55.773  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x46 
08-27 10:03:55.774  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 10:03:55.774  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 10:03:55.774  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12358
08-27 10:03:55.774  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12358
08-27 10:03:55.780   723   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 10:03:55.780   723   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x36, 0x87, ]
08-27 10:03:55.780   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:55.782  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x36 
08-27 10:03:55.783  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
01-01 11:33:28.588   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.783  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 10:03:55.784  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 310
08-27 10:03:55.784  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=310
08-27 10:03:55.784  2555  2732 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 49, 48]
08-27 10:03:55.785  2555  2732 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 10:03:55.790  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.597   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.799  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.606   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.615   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.624   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.633   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.642   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.842  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.651   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.660   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.854  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.859  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.669   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.678   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.876  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.879  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.687   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.890  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.696   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.705   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.714   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.913  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.723   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.922  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.732   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.741   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.750   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.946  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.759   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.957  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.768   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.964  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.777   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.976  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:55.981  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.786   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:55.989  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.795   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.804   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.813   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.014  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.822   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.024  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.831   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.031  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.840   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.040  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.849   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.048  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.858   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.057  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.867   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.065  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.876   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.076  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.082  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.885   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.894   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.091  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.903   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.099  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.912   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.109  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.115  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.921   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.125  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.930   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.132  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.939   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:28.948   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.142  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.149  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.957   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.158  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:28.966   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.166  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:28.975   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.176  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.182  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:56.183  1130  1192 W TransactionTracing: Could not find layer id -1
01-01 11:33:28.984   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.183  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.184  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:56.184  1130  1192 W TransactionTracing: Could not find layer id -1
01-01 11:33:28.993   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.192  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.196  1717  6526 D BufferPoolAccessor2.0: bufferpool2 0xb40000704eed41b8 : 3(72000 size) total buffers - 1(24000 size) used buffers - 221427/221430 (recycle/alloc) - 13/221428 (fetch/transfer)
01-01 11:33:29.002   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.200  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.210  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.011   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.217  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.020   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:29.029   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.226  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.039   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.234  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.048   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.244  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.251  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.057   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.260  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.066   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.264  2555  2734 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 10:03:56.265  2555  2734 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 10:03:56.266  3666  3873 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 10:03:56.267  2555  2734 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 10:03:56.267  2555  2734 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 10:03:56.268  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.268  2555  2734 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 10:03:56.268  2555  2734 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 10:03:56.268  3666  4251 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 10:03:56.268  3666  4251 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 10:03:56.268  3666  4251 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@a5646cd
08-27 10:03:56.268  3666  4251 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 10:03:56.268  3666  4251 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 10:03:56.268   723   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 10:03:56.268   723   741 I mpu_uart: [TIME-:6453]:create
01-01 11:33:29.075   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.277  1032  3574 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3666:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 10:03:56.277  1032  3574 E ActivityManager: java.lang.Throwable
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 10:03:56.277  1032  3574 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
01-01 11:33:29.084   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.279  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.280   723   737 E mpu_uart: send_buff: 3c080011158000b0
08-27 10:03:56.281   723   737 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 10:03:56.284  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.284   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:03:56.285   723   741 I mpu_uart: [TIME-:6453]:delete
08-27 10:03:56.285   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 10:03:56.285   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 10:03:56.285  2555  2734 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 10:03:56.286   723   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:56.286   723   741 I mpu_uart: [TIME-:6454]:create
01-01 11:33:29.093   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.293  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.294  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 10:03:56.294  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 10:03:56.294  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
01-01 11:33:29.102   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.305   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:56.305   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:29.111   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:29.120   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.318  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.129   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.327  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.138   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.336  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.147   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.346  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.156   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.353  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.165   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.364  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.174   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:29.183   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.386  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.192   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.395  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.201   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.403  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.210   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:29.219   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.416  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.420  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.228   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.429  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.237   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.437  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.246   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.447  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.255   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.454  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.264   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.463  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.273   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.471  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.282   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.480  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.291   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.488  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.300   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.497  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.505  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.309   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:29.318   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.514  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.521  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.327   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.531  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.336   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.538  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.345   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.548  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.354   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.555  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.363   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.564  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.372   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.572  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.381   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.584  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.391   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.589  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.399   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.598  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:29.407   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.606  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.416   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.617  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.623  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:29.426   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:56.632  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.658  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.669  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.692  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.705  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.728  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.740  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.761  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.774  2555  3001 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 10:03:56.775  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.778  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 10:03:56.778  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 10:03:56.779  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.779   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:56.779   723   740 I mpu_uart: [TIME-:6455]:create
08-27 10:03:56.797  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.830  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.848  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.881  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.900  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.901  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.921  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.926  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.941  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.959  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:56.973  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:56.996  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:57.014  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.015  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:57.032  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.129  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:57.133  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.234  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.285   723   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 10:03:57.305   723   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 10:03:57.306   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:57.306   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 10:03:57.335  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.436  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.537  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.638  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.740  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.840  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:57.941  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:58.042  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:30.990   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:30.999   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.008   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.017   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.026   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.035   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.232  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:58.232  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:58.234  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.044   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.245  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.053   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.062   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.071   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.268  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.080   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.280  6495  6520 D CCodecBufferChannel: [c2.android.opus.encoder#354] DEBUG: elapsed: n=4 [in=0 pipeline=0 out=0]
08-27 10:03:58.280  6495  6520 D PipelineWatcher: DEBUG: elapsed 0 / 4
08-27 10:03:58.280  6495  6520 D CCodecBufferChannel: [c2.qti.avc.encoder#182] DEBUG: elapsed: n=8 [in=0 pipeline=0 out=4]
08-27 10:03:58.280  6495  6520 D PipelineWatcher: DEBUG: elapsed 0 / 8
08-27 10:03:58.282  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:58.284  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:58.285   723   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-01 11:33:31.089   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.099   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.298  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.108   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.306   723   737 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 10:03:58.306   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:58.307   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:31.117   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.126   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.135   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.144   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.153   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.352  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.162   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.171   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.365  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.180   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.189   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.386  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.198   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.398  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.207   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.404  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.216   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.420  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.225   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.234   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.243   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.252   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.454  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.261   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.270   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.468  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:58.471  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.279   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.486  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.288   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.297   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.306   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.505  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.315   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.517  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.324   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.333   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.343   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.539  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.352   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.552  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:58.555  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.361   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.370   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.567  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.379   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.388   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.589  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.397   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.598  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.406   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.606  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.415   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.618  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.424   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.622  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:58.623  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:58.628  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:58.628  1130  1192 W TransactionTracing: Could not find layer id -1
01-01 11:33:31.433   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.636  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.442   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.451   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.460   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.657  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:58.666  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.469   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.478   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.487   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.496   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.691  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:58.701  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.505   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.708  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.514   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.523   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.721  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.532   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.541   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.742  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.550   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.752  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.559   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.758  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.568   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.769  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.577   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.777  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.586   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.785  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.596   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.794  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:58.803  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.605   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.811  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.614   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.623   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.820  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.632   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.828  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:58.836  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.641   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.845  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.650   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.854  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.659   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.862  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.668   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.871  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.677   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.879  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.686   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.888  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.695   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.895  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.704   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.905  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.713   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.722   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.731   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.740   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.749   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.947  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.758   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.960  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.767   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.776   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.982  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.785   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.794   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:58.996  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.803   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.812   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.014  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.821   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.024  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.830   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.839   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.848   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.857   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.866   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.065  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.875   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.078  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.885   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.894   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.097  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.098  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.098  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.099  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.902   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.912   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.109  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.921   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.930   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.134  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.939   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.948   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.145  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.150  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.957   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.966   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.162  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:31.975   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:31.984   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.183  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:31.993   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.192  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.002   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.201  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.011   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.211  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.020   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.217  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.222   723   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 10:03:59.222   723   741 I mpu_uart: [TIME-:6454]:delete
08-27 10:03:59.222   723   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x33, 0x36, 0x36, 0x31, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x1b, ]
08-27 10:03:59.222   723   738 V mpu_uart: mcu_info:s_log_print_cnt=3661,current_state=0, ota_state = 1
08-27 10:03:59.222   723   738 V mpu_uart:  >> log: 
08-27 10:03:59.227  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.029   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.038   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.234  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.235   723   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 10:03:59.236   723   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x35, 0x38, 0x20, 0x6d, 0x76, 0xa, 0x71, ]
08-27 10:03:59.236   723   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12358 mv
08-27 10:03:59.236   723   738 V mpu_uart:  >> log: 
08-27 10:03:59.242   723   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 10:03:59.242   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
01-01 11:33:32.047   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.244  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.249   723   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 10:03:59.249   723   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 10:03:59.249   723   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 10:03:59.249   723   738 V mpu_uart:  >> log: 
08-27 10:03:59.250   723   740 I mpu_uart: [TIME-:6455]:delete
01-01 11:33:32.056   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.251  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.254  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 10:03:59.256  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 10:03:59.256   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:59.256   723   740 I mpu_uart: [TIME-:6456]:create
08-27 10:03:59.259   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:03:59.259   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 10:03:59.259   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:59.259  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.260  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 10:03:59.261  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 10:03:59.261  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
01-01 11:33:32.065   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.268  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.270   723   737 E mpu_uart: send_buff: 3c0700111491af
08-27 10:03:59.270   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 10:03:59.270   723   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 10:03:59.270   723   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x38, 0xa3, ]
08-27 10:03:59.270   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:03:59.271   723   740 I mpu_uart: [TIME-:6456]:delete
01-01 11:33:32.074   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.274  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x38 
08-27 10:03:59.274  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 10:03:59.274  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 10:03:59.275  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12344
08-27 10:03:59.275  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12344
08-27 10:03:59.278  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.280   723   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 10:03:59.280   723   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 10:03:59.280   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
01-01 11:33:32.083   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.283  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 10:03:59.284  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 10:03:59.284  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 10:03:59.285  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.092   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.284  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 10:03:59.295  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.295  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
08-27 10:03:59.296  2555  2732 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 10:03:59.297  2555  2732 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
01-01 11:33:32.101   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.110   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.119   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.319  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.128   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.328  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.137   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.337  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.147   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.349  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.155   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.353  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.348  1579  1579 I tlog    : type=1400 audit(0.0:674): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
01-01 11:33:32.165   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.360   568   568 W hwservicemanage: type=1400 audit(0.0:675): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 10:03:59.368  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.174   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.371  1579  1594 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 10:03:59.372  1579  1594 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 10:03:59.372  1579  1594 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-01 11:33:32.177   568   568 I binder  : 568:568 transaction failed 29201/-1, size 28-8 line 3410
01-01 11:33:32.177   568   568 I binder  : send failed reply for transaction 1335225 to 1579:1594
08-27 10:03:59.368   568   568 W hwservicemanage: type=1400 audit(0.0:676): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 10:03:59.376  1579  1594 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 10:03:59.376  1579  1594 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 10:03:59.376  1579  1594 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-01 11:33:32.181   568   568 I binder  : 568:568 transaction failed 29201/-1, size 28-8 line 3410
01-01 11:33:32.181   568   568 I binder  : send failed reply for transaction 1335243 to 1579:1594
01-01 11:33:32.183   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.387  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.192   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.201   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.397  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.404  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.210   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.409  1056  1056 D vendor.qti.vibrator: Vibrator on for timeoutMs: 20
08-27 10:03:59.410  1056  8873 D vendor.qti.vibrator: Starting on on another thread
08-27 10:03:59.412  1111  6534 D AudioFlinger: ro.audio.silent will be ignored for threads on AUDIO_DEVICE_OUT_REMOTE_SUBMIX
08-27 10:03:59.416  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.216  8130  8130 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
01-01 11:33:32.219   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.225  8130  8130 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
08-27 10:03:59.421  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.430  1056  8873 D vendor.qti.vibrator: Notifying on complete
08-27 10:03:59.431  1056  1056 D vendor.qti.vibrator: QTI Vibrator off
08-27 10:03:59.431  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.228   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.234  8130  8130 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
01-01 11:33:32.237   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.246   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.255   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.455  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.264   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.464  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.273   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.282   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.480  1111  6534 D AudioFlinger: mixer(0xb400007ad4e71990) throttle end: throttle time(23)
01-01 11:33:32.291   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.489  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.300   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.501  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.309   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.318   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.327   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.523  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.336   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.535  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.540  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.345   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.354   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.551  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.363   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.372   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.573  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.381   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.583  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.390   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.590  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.399   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.600  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.408   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.606  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.417   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.616  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.426   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.624  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.435   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.634  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.641  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.444   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.650  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.453   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.657  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.462   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.666  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.471   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.674  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.480   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.684  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.691  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.497   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.701  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.506   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.708  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.515   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.717  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.524   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.725  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.533   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.734  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.542   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.740  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.742  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.551   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.751  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.560   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.759  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.762  2555  2734 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 10:03:59.765  2555  2734 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 10:03:59.766  3666  3873 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
01-01 11:33:32.569   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.769  2555  2734 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 10:03:59.769  2555  2734 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 10:03:59.769  2555  2734 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 10:03:59.770  2555  2734 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 10:03:59.770   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 10:03:59.770   723   740 I mpu_uart: [TIME-:6457]:create
08-27 10:03:59.771  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.578   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.776  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.587   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.784   723   737 E mpu_uart: send_buff: 3c080011158000b0
08-27 10:03:59.784   723   737 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 10:03:59.784  3666  3863 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 10:03:59.784  3666  3863 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 10:03:59.784  3666  3863 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@fdccb82
08-27 10:03:59.784  3666  3863 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 10:03:59.784  3666  3863 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 10:03:59.788  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.789   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:03:59.789   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 10:03:59.789   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 10:03:59.790   723   740 I mpu_uart: [TIME-:6457]:delete
08-27 10:03:59.790  1032  8016 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3666:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 10:03:59.790  1032  8016 E ActivityManager: java.lang.Throwable
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 10:03:59.790  1032  8016 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 10:03:59.791  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 10:03:59.791  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 10:03:59.792  2555  2732 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 10:03:59.794  2555  2734 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
01-01 11:33:32.597   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.796   723   740 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:03:59.796   723   740 I mpu_uart: [TIME-:6458]:create
01-01 11:33:32.606   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.810  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:03:59.810   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:03:59.810   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:32.615   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.624   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.818  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.633   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.642   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.842  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.842  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.842  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:03:59.843  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.651   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.660   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.854  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:03:59.861  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.669   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.875  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.678   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.878  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.687   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.889  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.696   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.705   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.714   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.912  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.723   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.923  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.732   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.741   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.945  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.750   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.759   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.956  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.768   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.777   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.978  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.786   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.987  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.795   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:03:59.996  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.804   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.006  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.813   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.012  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.822   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.023  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.831   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.029  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.840   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.039  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.849   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.858   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.867   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.063  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.071  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.876   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.080  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.885   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.894   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.090  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.097  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.903   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:32.912   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.106  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.113  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.118  1059  8556 I QC2Comp : [avcE_42] Stats: Pending(0) i/p-done(0) Works: Q: 3027/Done 3027|Work-Rate: Q(31.0/s Avg=1.7/s) Done(30.996/s Avg=1.695/s)| Stream: 59.87fps 1.6Mbps
08-27 10:04:00.118  1059  8556 I QC2Comp : Mem-usage:  [In-2D - 65 bufs 24.375 MB] [1D-44 - 17 bufs 3.984 MB]
08-27 10:04:00.118  1059  8556 I QC2Comp : Total Mem-usage: 28.359 MB
01-01 11:33:32.922   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.122  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.931   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.130  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.940   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.139  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.949   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.148  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.958   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.157  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.967   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.164  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.976   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.174  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.181  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:32.985   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.190  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:32.994   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.003   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.198  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.206  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.012   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.215  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.021   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.224  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.030   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.232  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.039   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.241  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.048   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.248  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.057   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.257  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.066   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.265  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.075   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.274  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.277  2555  3001 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 10:04:00.277  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 10:04:00.277  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 10:04:00.278   723   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:04:00.278   723   741 I mpu_uart: [TIME-:6459]:create
01-01 11:33:33.084   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.283  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.093   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.290  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.102   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.111   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.120   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.129   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.333  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.334  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.334  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.138   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.337  6495  6518 D BufferPoolAccessor2.0: bufferpool2 0xb4000078dd46bf08 : 5(19200 size) total buffers - 4(15360 size) used buffers - 221635/221640 (recycle/alloc) - 1091/443271 (fetch/transfer)
01-01 11:33:33.147   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.347  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.156   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.165   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.174   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.183   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.384  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.192   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.201   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.400  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.210   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.219   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.228   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.237   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.436  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.246   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.446  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.255   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.264   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.469  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.273   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.282   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.479  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.291   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.300   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.502  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.309   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.513  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.318   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.520  1059  8556 D BufferPoolAccessor2.0: bufferpool2 0xee4806e8 : 4(983040 size) total buffers - 3(737280 size) used buffers - 3029/3046 (recycle/alloc) - 39/3043 (fetch/transfer)
08-27 10:04:00.520  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.327   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.529  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.336   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.345   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.354   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.552  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.552  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.552  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.552  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.553  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.363   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.565  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.372   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.570  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.381   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.581  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.390   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.587  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.399   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.597  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.604  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.408   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.613  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.417   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.621  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.426   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.435   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.631  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.638  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.444   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.646  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.454   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.654  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.463   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.665  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.472   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.671  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.481   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.680  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.490   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.688  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.499   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.697  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.508   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.705  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.714  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.517   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.526   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.722  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.535   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.733  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.544   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.739  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.748  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.553   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.756  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.562   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.765  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.571   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.773  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.580   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.781  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.589   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.789  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.790   723   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-01 11:33:33.598   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.798  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.607   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.806  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.810   723   737 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 10:04:00.811   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:04:00.811   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:33.616   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.816  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.625   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.823  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.634   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.833  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.643   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.840  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:00.849  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.652   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.857  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.661   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.670   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.866  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.874  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.679   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.882  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.688   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.891  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.697   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.901  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.706   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.907  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.907  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.907  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.908  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.908  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:00.908  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.715   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.918  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.724   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.925  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.733   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.934  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.742   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.942  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.751   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.951  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.760   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.959  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.769   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.968  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.778   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.976  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.787   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:00.985  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:00.993  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.796   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.805   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.005  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.814   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.823   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.026  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.832   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.842   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.040  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.044  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.851   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.860   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.057  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.060  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.869   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.074  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.878   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.077  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.887   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.088  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.896   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.094  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.905   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.105  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.914   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.923   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.932   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.128  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.941   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.141  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.145  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.950   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.959   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.156  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:33.968   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:33.977   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.178  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:33.986   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.187  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.196  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.003   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.012   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.208  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.021   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.030   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.229  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.039   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.239  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.048   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.246  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.057   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.256  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.066   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.263  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.075   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.275  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.084   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.280  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:01.290  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.093   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.102   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.111   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.312  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:01.313  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.120   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.319  1717  6526 D BufferPoolAccessor2.0: bufferpool2 0xb40000704eed41b8 : 3(72000 size) total buffers - 1(24000 size) used buffers - 221683/221686 (recycle/alloc) - 13/221684 (fetch/transfer)
08-27 10:04:01.322  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.129   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.330  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.138   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.342  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.147   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.348  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.156   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.357  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.165   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.364  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.174   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.375  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.183   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.381  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.192   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.390  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.201   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.398  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.210   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.409  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.219   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.228   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.432  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.237   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.442  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.246   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.446  2492  3940 D MonitorTool_MonitorAppService: [08-27 10:04:01.445] [pool-3-thread-1:36] Starting to check status of 3 apps
08-27 10:04:01.449  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.255   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.460  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.462  2492  3940 V MonitorTool_MonitorAppService: [08-27 10:04:01.457] [pool-3-thread-1:36] App[drvsys] not detected running
01-01 11:33:34.264   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.463  2492  3940 V MonitorTool_MonitorAppService: [08-27 10:04:01.463] [pool-3-thread-1:36] App[drvsys] running status: Not running
08-27 10:04:01.465  2492  3940 W MonitorTool_MonitorAppService: [08-27 10:04:01.463] [pool-3-thread-1:36] App[drvsys] not running, attempting to start
08-27 10:04:01.468  2492  3940 E MonitorTool_MonitorAppService: [08-27 10:04:01.467] [pool-3-thread-1:36] Could not get launch intent for app[drvsys], it might not be a launchable app
01-01 11:33:34.273   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.472  2492  3940 V MonitorTool_MonitorAppService: [08-27 10:04:01.470] [pool-3-thread-1:36] App[com.thundercomm.testapp] not detected running
08-27 10:04:01.472  2492  3940 V MonitorTool_MonitorAppService: [08-27 10:04:01.472] [pool-3-thread-1:36] App[com.thundercomm.testapp] running status: Not running
08-27 10:04:01.474  2492  3940 W MonitorTool_MonitorAppService: [08-27 10:04:01.474] [pool-3-thread-1:36] App[com.thundercomm.testapp] not running, attempting to start
08-27 10:04:01.476  2492  3940 E MonitorTool_MonitorAppService: [08-27 10:04:01.476] [pool-3-thread-1:36] Could not get launch intent for app[com.thundercomm.testapp], it might not be a launchable app
08-27 10:04:01.478  2492  3940 V MonitorTool_MonitorAppService: [08-27 10:04:01.477] [pool-3-thread-1:36] App[com.ssol.titanApp] not detected running
08-27 10:04:01.478  2492  3940 V MonitorTool_MonitorAppService: [08-27 10:04:01.478] [pool-3-thread-1:36] App[com.ssol.titanApp] running status: Not running
08-27 10:04:01.478  2492  3940 W MonitorTool_MonitorAppService: [08-27 10:04:01.478] [pool-3-thread-1:36] App[com.ssol.titanApp] not running, attempting to start
01-01 11:33:34.282   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.480  2492  3940 E MonitorTool_MonitorAppService: [08-27 10:04:01.479] [pool-3-thread-1:36] Could not get launch intent for app[com.ssol.titanApp], it might not be a launchable app
08-27 10:04:01.482  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.291   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.491  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.300   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.498  1130  1192 W TransactionTracing: Could not find layer id -1
08-27 10:04:01.499  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:01.503  1130  1192 W TransactionTracing: Could not find layer id -1
01-01 11:33:34.309   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.510  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.318   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.516  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.327   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.526  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.533  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.336   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.543  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.345   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.355   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.550  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:01.558  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.364   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.567  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.373   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.577  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.382   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.584  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.391   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.590  6495  6520 D CCodecBufferChannel: [c2.android.opus.encoder#354] DEBUG: elapsed: n=4 [in=0 pipeline=0 out=0]
08-27 10:04:01.590  6495  6520 D PipelineWatcher: DEBUG: elapsed 0 / 4
08-27 10:04:01.590  6495  6520 D CCodecBufferChannel: [c2.qti.avc.encoder#182] DEBUG: elapsed: n=8 [in=0 pipeline=0 out=4]
08-27 10:04:01.590  6495  6520 D PipelineWatcher: DEBUG: elapsed 0 / 8
08-27 10:04:01.592  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.400   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.601  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.409   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.610  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.418   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.617  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.427   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.626  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.436   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.635  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.445   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.644  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.454   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.651  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.463   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.660  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.472   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.668  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.481   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.678  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:01.685  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.490   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.694  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.499   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.702  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.508   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.712  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.517   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.526   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.535   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.544   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.553   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.754  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.562   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.767  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.571   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.580   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.589   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.790   723   739 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-01 11:33:34.598   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.607   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.805  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:01.811   723   737 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 10:04:01.811   723   737 E mpu_uart: send_buff: 3c0700111487b9
08-27 10:04:01.814   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-01 11:33:34.616   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.818  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.625   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.634   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.643   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.652   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.855  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.661   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.863  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.670   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.679   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.688   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.697   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.706   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.906  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.715   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.917  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.724   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.923  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.733   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.936  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.742   539   539 I get_disable_lcd_state: s_disable_lcd = 0
01-01 11:33:34.751   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.949  1056  1056 D vendor.qti.vibrator: QTI Vibrator off
08-27 10:04:01.952  1032  1994 I WindowManager: Ignoring HOME; event canceled.
01-01 11:33:34.760   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.956  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:01.965  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.769   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.974  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-01 11:33:34.786   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.985  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
01-01 11:33:34.796   539   539 I get_disable_lcd_state: s_disable_lcd = 0
08-27 10:04:01.990  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:01.999  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.007  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.016  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.024  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.034  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.041  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.051  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.058  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.068  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.075  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.086  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.092  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.101  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.109  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.119  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.126  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.136  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.143  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.153  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.160  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.169  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.177  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.187  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.194  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.203  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.211  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.221  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.228  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.237  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.245  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.256  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.357  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.361  1023  1253 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 10:04:02.458  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.472   894  1971 I r_submix: out_standby()
08-27 10:04:02.559  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.661  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.720   723   738 D mpu_uart: [MSG-P:R-M]:recved H:70, L:0
08-27 10:04:02.720   723   740 I mpu_uart: [TIME-:6458]:delete
08-27 10:04:02.720   723   738 V mpu_uart: recv data buf:[0x3c, 0x46, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3d, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x33, 0x36, 0x36, 0x32, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x18, ]
08-27 10:04:02.720   723   738 V mpu_uart: mcu_info:s_log_print_cnt=3662,current_state=0, ota_state = 1
08-27 10:04:02.720   723   738 V mpu_uart:  >> log: 
01-01 11:33:35.537  8813  8813 I (virq   : irq_count)- 3:3946008 300:619265 57:522823 263:143274 220:140730 217:44306 51:36727 223:32413 47:32173 103:28038
01-01 11:33:35.537  8813  8813 I (cpu    : irq_count)- 0:1869041 1:1009924 2:1001690 3:1767489 4:9842 5:17759 6:21091 7:11720
08-27 10:04:02.735   723   738 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
01-01 11:33:35.537  8813  8813 I (ipi    : irq_count)- 0:6884661 1:2181086 2:0 3:0 4:0 5:449884 6:0
08-27 10:04:02.736   723   738 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x31, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x38, 0x37, 0x20, 0x6d, 0x76, 0xa, 0x72, ]
08-27 10:04:02.736   723   738 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=310 r/s,vbat=12387 mv
08-27 10:04:02.736   723   738 V mpu_uart:  >> log: 
08-27 10:04:02.740   723   737 E mpu_uart: send_buff: 3c0700111488b6
08-27 10:04:02.740   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 10:04:02.749   723   738 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 10:04:02.749   723   738 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 10:04:02.749   723   738 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 10:04:02.749   723   738 V mpu_uart:  >> log: 
08-27 10:04:02.750   723   741 I mpu_uart: [TIME-:6459]:delete
08-27 10:04:02.751  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 10:04:02.752  2555  3001 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 10:04:02.752   723   741 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 10:04:02.753   723   741 I mpu_uart: [TIME-:6460]:create
08-27 10:04:02.759   723   738 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 10:04:02.759   723   738 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 10:04:02.760   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:04:02.761  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 10:04:02.761  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 10:04:02.762  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 10:04:02.763  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.769   723   737 E mpu_uart: send_buff: 3c0700111491af
08-27 10:04:02.770   723   738 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 10:04:02.770   723   737 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 10:04:02.770   723   738 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x63, 0xf8, ]
08-27 10:04:02.770   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:04:02.772  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x63 
08-27 10:04:02.773  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 10:04:02.773  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 10:04:02.774  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12387
08-27 10:04:02.774  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12387
08-27 10:04:02.780   723   738 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 10:04:02.781   723   738 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x36, 0x87, ]
08-27 10:04:02.781   723   741 I mpu_uart: [TIME-:6460]:delete
08-27 10:04:02.782   723   739 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 10:04:02.786  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x36 
08-27 10:04:02.787  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 10:04:02.788  2555  2732 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 10:04:02.788  2555  2732 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 310
08-27 10:04:02.789  2555  2732 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=310
08-27 10:04:02.790  2555  2732 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 49, 48]
08-27 10:04:02.794  2555  2732 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 10:04:02.863  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:02.964  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:03.065  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
08-27 10:04:03.166  1059  8556 I QC2C2DEngine: calcYSize: unsupported or RGB color format, RGBA8888_UBWC(-**********)
