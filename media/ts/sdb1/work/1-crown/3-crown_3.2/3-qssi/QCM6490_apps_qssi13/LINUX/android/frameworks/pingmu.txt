--------- beginning of system
08-27 08:30:03.080  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.087  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.097  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.097  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.099  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.108  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.110  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.118  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
--------- beginning of main
08-27 08:30:03.122   734   750 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:03.127  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.127  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.129  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.139  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.140  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
--------- beginning of kernel
01-02 03:47:16.667   582   582 I logd    : logdr: UID=2000 GID=2000 PID=4732 b tail=0 logMask=99 pid=0 start=0ns deadline=0ns
08-27 08:30:03.142   734   748 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:30:03.142   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:03.142   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:03.140  1591  1591 I tlog    : type=1400 audit(0.0:155): avc: denied { rename } for name="-00001_kernel_00054_700102_034655.log.ing" dev="dm-6" ino=14909 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:30:03.140  1591  1591 I tlog    : type=1400 audit(0.0:156): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
08-27 08:30:03.144   579   579 W hwservicemanage: type=1400 audit(0.0:157): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:30:03.148   579   579 W hwservicemanage: type=1400 audit(0.0:158): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:30:03.148  1591  1591 I tlog    : type=1400 audit(0.0:159): avc: denied { setattr } for name="-00001_kernel_00055_250827_083003.log.ing" dev="dm-6" ino=15142 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
01-02 03:47:16.674   579   579 I binder  : 579:579 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:47:16.674   579   579 I binder  : send failed reply for transaction 59208 to 1591:1610
01-02 03:47:16.677   579   579 I binder  : 579:579 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:47:16.677   579   579 I binder  : send failed reply for transaction 59212 to 1591:1610
08-27 08:30:03.148  1591  1610 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:30:03.148  1591  1610 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:03.148  1591  1610 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:30:03.150  1591  1610 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:30:03.150  1591  1610 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:03.150  1591  1610 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:30:03.157  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.157  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.159  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.169  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.171  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.178  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.187  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.188  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.189  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.200  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.201  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.209  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.218  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.218  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.220  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.230  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.232  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.240  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.248  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.249  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.250  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.261  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.262  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.270  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.279  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.279  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.280  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.292  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.292  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.300  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.309  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.309  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.311  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.322  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.323  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.331  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.339  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.340  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.341  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.353  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.353  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.361  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.370  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.370  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.371  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.383  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.383  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.392  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.400  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.400  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.401  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.414  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.414  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.422  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.422   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4189 went down
08-27 08:30:03.425   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418a went down
08-27 08:30:03.429   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418b went down
08-27 08:30:03.430  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.431  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.432  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.432   579   579 W hwservicemanage: type=1400 audit(0.0:160): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:30:03.437  1591  1610 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
01-02 03:47:16.964   579   579 I binder  : 579:579 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:47:16.964   579   579 I binder  : send failed reply for transaction 59216 to 1591:1610
08-27 08:30:03.437  1591  1610 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:03.437  1591  1610 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:47:16.965   579   579 W audit   : audit_lost=21 audit_rate_limit=5 audit_backlog_limit=64
01-02 03:47:16.965   579   579 E audit   : rate limit exceeded
01-02 03:47:16.971   579   579 I binder  : 579:579 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:47:16.971   579   579 I binder  : send failed reply for transaction 59220 to 1591:1610
08-27 08:30:03.443  1591  1610 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:30:03.443  1591  1610 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:03.443  1591  1610 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:30:03.444  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.444  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.452  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.461  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.461  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.462  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.474  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.474  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.482  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.491  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.491  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.492  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.505  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.505  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.513  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.522  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.522  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.523  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.535  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.535  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.543  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.552  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.552  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.553  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.565  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.565  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.574  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.583  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.583  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.583  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.596  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.596  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.604  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.613  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.613  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.613  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.626  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.626  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.634  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.644  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.644  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.644  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.656  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.657  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.665  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.674  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.674  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.674  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.687  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.687  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.695  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.704  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.704  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.705  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.717  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.717  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.725  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.735  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.735  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.735  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.747  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.748  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.756  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.765  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.765  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.765  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.778  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.778  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.786  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.795  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.796  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.796  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.808  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.808  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.816  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.826  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.826  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.826  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.838  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.838  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.847  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.856  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.856  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.857  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.869  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.869  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.877  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.886  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.887  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.887  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.899  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.899  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.907  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.917  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.917  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.917  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.929  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.929  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.938  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.947  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.947  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.947  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.959  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.960  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.968  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.977  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.977  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.978  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.990  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.990  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:03.998  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.008  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.008  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.008  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.020  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.020  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.028  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.038  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.038  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.038  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.051  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.051  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.059  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.068  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.068  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.068  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.081  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.082  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.089  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.098  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.099  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.099  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.112  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.112  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.119  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.122   734   750 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:04.129  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.129  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.129  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.142  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.142  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.142   734   748 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:30:04.143   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:04.143   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:04.149  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.159  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.159  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.159  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.172  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.173  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.180  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.190  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.190  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.190  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.203  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.203  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.210  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.220  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.220  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.220  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.221  1252  1377 I ActivityManager: Waited long enough for: ServiceRecord{bc01d36 u0 com.qualcomm.qtil.btdsda/.BtDsDaService}
08-27 08:30:04.233  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.233  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.240  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.250  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.251  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.251  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.264  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.264  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.271  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.281  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.281  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.281  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.294  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.294  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.302  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.311  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.312  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.312  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.325  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.325  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.332  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.342  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.342  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.342  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.355  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.356  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.362  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.373  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.373  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.373  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.386  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.386  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.393  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.404  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.404  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.404  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.416  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.417  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.424  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.429   902   929 E ssgtzd  : Modemcomm: Attempt 3 : Init Instance failed with -3
08-27 08:30:04.434  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.434  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.435  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.447  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.448  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.455  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.465  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.465  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.465  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.478  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.478  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.486  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.496  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.496  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.496  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.508  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.509  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.516  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.527  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.527  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.527  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.539  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.540  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.547  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.557  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.557  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.558  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.570  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.571  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.577  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.588  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.588  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.589  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.601  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.602  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.608  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.619  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.619  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.619  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.631  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.632  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.639  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.649  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.650  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.650  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.662  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.663  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.669  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.680  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.681  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.681  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.693  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.694  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.700  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.711  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.712  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.712  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.724  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.724  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.730  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.742  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.743  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.743  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.755  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.755  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.761  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.773  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.774  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.774  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.785  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.786  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.792  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.804  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.805  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.805  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.816  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.816  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.822  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.835  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.835  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.836  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.846  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.847  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.853  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.865  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.866  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.866  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.877  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.878  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.883  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.896  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.897  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.897  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.908  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.908  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.914  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.926  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.927  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.928  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.938  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.939  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.944  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.957  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.958  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.959  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.969  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.969  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.975  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.988  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.988  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.989  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:04.999  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.000  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.005  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.018  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.019  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.020  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.030  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.030  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.036  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.049  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.050  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.050  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.060  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.061  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.063   734   749 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:30:05.063   734  3710 I mpu_uart: [TIME-:21]:delete
08-27 08:30:05.063   734   749 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x35, 0x34, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x2b, ]
08-27 08:30:05.063   734   749 V mpu_uart: mcu_info:s_log_print_cnt=20354,current_state=0, ota_state = 1
08-27 08:30:05.063   734   749 V mpu_uart:  >> log: 
08-27 08:30:05.066  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.078   734   749 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:30:05.078   734   749 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x37, 0x32, 0x20, 0x6d, 0x76, 0xa, 0x71, ]
08-27 08:30:05.078   734   749 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12372 mv
08-27 08:30:05.078   734   749 V mpu_uart:  >> log: 
08-27 08:30:05.080  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.080  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.081  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.083   734   748 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:30:05.083   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:30:05.091  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.091   734   749 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:30:05.091   734   751 I mpu_uart: [TIME-:22]:delete
08-27 08:30:05.092   734   749 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:30:05.092   734   749 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:30:05.092   734   749 V mpu_uart:  >> log: 
08-27 08:30:05.092  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.092  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:30:05.092  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:30:05.093   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:05.094   734   751 I mpu_uart: [TIME-:23]:create
08-27 08:30:05.097  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.101   734   749 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:05.101   734   749 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:30:05.102   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:05.103  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:30:05.103  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:30:05.103  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:30:05.111  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.111  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.112  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.112   734   748 E mpu_uart: send_buff: 3c0700111491af
08-27 08:30:05.112   734   749 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:30:05.112   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:30:05.112   734   749 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x46, 0xdd, ]
08-27 08:30:05.112   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:05.113  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x46 
08-27 08:30:05.113  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:30:05.113  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:30:05.114  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12358
08-27 08:30:05.114  2544  2727 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12358
08-27 08:30:05.121  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.122  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.123   734   749 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:30:05.123   734   749 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 08:30:05.123   734   751 I mpu_uart: [TIME-:23]:delete
08-27 08:30:05.123   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:05.125  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 08:30:05.125  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:30:05.126  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:30:05.126  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 08:30:05.126  2544  2727 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 08:30:05.127  2544  2727 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 08:30:05.127  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.128  2544  2727 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:30:05.141  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.141  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.142  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.152  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.153  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.158  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.172  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.172  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.172  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.182  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.183  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.188  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.202  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.202  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.204  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.213  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.214  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.219  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.233  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.233  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.234  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.244  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.244  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.249  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.264  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.264  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.265  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.275  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.275  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.280  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.295  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.295  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.296  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.305  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.306  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.310  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.325  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.325  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.327  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.336  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.336  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.341  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.356  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.356  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.357  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.367  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.367  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.372  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.388  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.388  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.388  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.398  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.398  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.402  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.419  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.419  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.419  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.428  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.428  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.433  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.433   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418c went down
08-27 08:30:05.436   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418d went down
08-27 08:30:05.448   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418e went down
08-27 08:30:05.449  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.450  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.450  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.459  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.459  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.464  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.481  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.481  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.481  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.490  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.490  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.494  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.512  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.512  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.513  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.520  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.520  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.525  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.543  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.543  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.543  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.551  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.551  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.555  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.574  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.574  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.574  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.581  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.581  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.586  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.604  2544  2730 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:30:05.604  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.605  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.605  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.605  2544  2730 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:30:05.607  3463  3779 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:30:05.608  3463  3770 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:30:05.608  3463  3770 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:30:05.608  3463  3770 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@ac26de3
08-27 08:30:05.609  3463  3770 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:30:05.609  3463  3770 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:30:05.611  2544  2730 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:30:05.611  2544  2730 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:30:05.611  1252  2205 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3463:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:30:05.611  1252  2205 E ActivityManager: java.lang.Throwable
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:30:05.611  1252  2205 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:30:05.611  2544  2730 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:30:05.612  2544  2730 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:30:05.612  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.612  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.612   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:30:05.612   734   751 I mpu_uart: [TIME-:24]:create
08-27 08:30:05.613   734   748 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:30:05.613   734   748 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:30:05.616  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.617   734   749 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:05.617   734   751 I mpu_uart: [TIME-:24]:delete
08-27 08:30:05.617   734   749 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:30:05.617   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:30:05.618  2544  2730 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:30:05.618  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:30:05.618   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:05.619   734   751 I mpu_uart: [TIME-:25]:create
08-27 08:30:05.619  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:30:05.619  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:30:05.635  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.635  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.635  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.637   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:05.638   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:05.642  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.642  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.647  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.666  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.666  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.666  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.673  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.673  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.677  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.696  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.696  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.696  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.704  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.704  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.708  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.727  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.727  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.727  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.734  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.734  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.738  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.757  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.757  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.757  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.765  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.765  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.768  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.787  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.787  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.787  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.795  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.795  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.799  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.818  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.818  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.818  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.825  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.826  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.830  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.848  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.848  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.849  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.856  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.856  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.861  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.879  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.879  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.879  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.886  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.886  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.891  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.429   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.910  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.910  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.910  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.438   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.917  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.917  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.447   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.921  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.456   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.465   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.941  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.941  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.941  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.947  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.947  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.474   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.952  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.483   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.492   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.972  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.972  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.972  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.502   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:05.977  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.978  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:05.982  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.511   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.520   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.000  1252  1361 I DropBoxManagerService: add tag=system_app_strictmode isTagEnabled=true flags=0x2
08-27 08:30:06.002  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.002  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.002  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.529   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.008  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.008  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.013  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.538   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.015  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.015  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.547   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.556   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.033  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.033  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.033  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.038  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.039  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.565   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.043  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.574   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.049  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.049  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.583   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.063  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.063  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.063  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.592   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.069  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.069  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.073  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.601   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.082  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.082  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.610   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.093  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.619   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.094  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.094  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.099  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.099  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.099  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.099  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.628   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.104  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.637   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.116  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.116  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.646   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.124  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.124  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.124  2544  3893 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:30:06.124  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.124  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:30:06.124  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:30:06.125   734  3710 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:06.125   734  3710 I mpu_uart: [TIME-:26]:create
01-02 03:47:19.655   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.130  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.130  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.135  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.664   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.673   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.150  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.151  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.154  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.154  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.154  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.682   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.160  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.160  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.165  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.691   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.167  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.167  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.700   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.184  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.184  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.185  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.185  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.709   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.186  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.190  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.190  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.718   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.195  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.727   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.200  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.200  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.736   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.215  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.216  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.217  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.218  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.218  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.221  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.746   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.221  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.226  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.754   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.234  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.235  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.764   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.246  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.247  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.247  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.773   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.251  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.251  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.251  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.251  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.256  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.782   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.791   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.268  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.268  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.800   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.276  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.277  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.278  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.282  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.282  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.809   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.285  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.286  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.286  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.818   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.827   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.302  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.303  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.307  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.308  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.308  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.836   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.312  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.312  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.317  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.845   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.319  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.319  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.854   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.336  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.336  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.337  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.863   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.338  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.339  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.342  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.342  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.872   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.347  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.353  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.353  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.881   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.890   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.367  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.369  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.369  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.369  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.370  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.372  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.372  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.899   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.377  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.908   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.386  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.387  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.917   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.398  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.399  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.399  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.926   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.403  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.403  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.404  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.404  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.408  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.935   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:19.944   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.420  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.421  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.428  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.953   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.430  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.430  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.433  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.433  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.962   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.438  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.438  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.438  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.971   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.448   902   929 E ssgtzd  : Modemcomm: Attempt 4 : Init Instance failed with -3
08-27 08:30:06.455  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.455  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:19.981   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.458  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.461  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.461  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.464  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.464  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.990   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.469  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:19.999   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.472  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.472  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.008   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.489  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.489  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.489  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.017   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.491  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.491  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.494  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.494  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.499  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.026   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.505  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.506  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.035   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.044   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.519  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.522  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.523  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.523  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.524  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.525  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.525  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.053   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.530  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.062   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.539  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.540  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.071   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.549  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.552  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.080   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.555  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.555  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.555  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.557  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.557  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.560  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.089   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.098   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.574  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.574  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.580  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.107   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.582  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.585  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.585  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.586  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.116   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.590  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.591  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.591  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.125   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.134   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.607  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.608  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.611  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.614  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.616  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.616  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.616  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.143   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.618   734   750 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:06.621  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.624  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.625  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.152   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.161   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.638   734   748 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:30:06.638   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:06.638   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:06.641  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.641  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.642  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.644  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.170   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.646  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.647  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.647  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.651  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.179   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.658  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.658  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.188   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.672  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.197   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.674  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.676  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.676  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.677  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.677  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.678  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.206   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.682  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.216   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.225   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.702  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.705  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.707  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.707  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.708  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.234   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.712  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.250   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.727  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.727  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.259   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.733  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.735  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.737  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.738  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.739  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.742  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.268   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.277   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.286   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.760  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.761  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.764  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.766  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.768  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.769  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.295   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.769  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.773  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.304   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.313   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.794  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.796  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.322   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.798  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.799  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.800  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.803  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.331   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.812  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.812  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.341   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.824  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.349   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.827  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.828  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.830  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.830  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.359   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.834  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.368   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.845  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.845  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.377   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.855  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.857  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.859  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.860  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.860  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.386   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.864  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.395   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.404   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.885  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.887  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.413   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.889  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.891  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.891  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.895  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.422   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.896  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.897  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.431   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.440   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.915  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.915  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.916  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.918  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.919  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.921  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.921  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.449   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.925  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.458   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.467   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.946  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.947  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.947  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:06.949  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.476   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.950  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.951  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.952  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.956  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.485   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.964  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.964  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.494   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.977  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.503   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.979  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.980  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.982  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:06.982  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.512   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.986  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.521   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:06.998  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:06.998  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.530   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.007  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.010  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.010  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.540   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.012  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.013  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.015  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.016  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.016  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.549   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.032  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.033  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.050  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.576   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.585   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.066  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.066  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.594   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.070  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.071  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.071  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.074  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.075  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.603   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.077  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.083  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.083  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.612   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.621   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.097  1046  1046 D vendor.qti.vibrator: Vibrator on for timeoutMs: 20
08-27 08:30:07.099  1046  4739 D vendor.qti.vibrator: Starting on on another thread
08-27 08:30:07.104  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.104  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.105  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.107  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.627     7     7 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
01-02 03:47:20.634   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.107  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.107  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.116  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.117  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.119  1046  4739 D vendor.qti.vibrator: Notifying on complete
01-02 03:47:20.639   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.640     7     7 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
01-02 03:47:20.648     7     7 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
01-02 03:47:20.655   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.131  1046  1046 D vendor.qti.vibrator: QTI Vibrator off
08-27 08:30:07.132   903  2085 D audio_hw_primary: adev_get_parameters:vr_audio_mode_on
01-02 03:47:20.659   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.134  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.134  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.134  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.135  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.135  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.137  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.138  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.138  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.668   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.144  1102  4676 D AF::TrackHandle: OpPlayAudio: track:55 usage:13 not muted
01-02 03:47:20.677   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.156  1252  2083 I AudioTrack: createTrack_l(0): AUDIO_OUTPUT_FLAG_FAST successful; frameCount 0 -> 4512
01-02 03:47:20.686   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.164  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.165  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.165  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.167  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.167  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.167  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.169  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.169  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.695   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.704   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.184  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.184  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.709   315   315 I (virq   : irq_count)- 3:56694 217:44939 47:18346 298:8940 57:6932 10:4245 313:3602 263:1825 300:1679 220:1577
01-02 03:47:20.709   315   315 I (cpu    : irq_count)- 0:99340 1:9535 2:14252 3:7574 4:6261 5:5964 6:6653 7:7709
01-02 03:47:20.709   315   315 I (ipi    : irq_count)- 0:107619 1:48810 2:0 3:0 4:0 5:49474 6:0
01-02 03:47:20.713   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.195  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.195  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.196  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.196   903  2085 E FMQ     : grantorIdx must be less than 3
01-02 03:47:20.722   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.197   903  2085 E FMQ     : grantorIdx must be less than 3
08-27 08:30:07.198  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.199  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.199  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.201  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.201  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.731   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.205   903  4741 D audio_hw_primary: start_output_stream: enter: stream(0xe3244330)usecase(1: low-latency-playback) devices(0x2) is_haptic_usecase(0)
08-27 08:30:07.209   579   579 I hwservicemanager: getTransport: Cannot find entry android.hardware.power@1.2::IPower/default in either framework or device VINTF manifest.
08-27 08:30:07.211   903  4741 E audio_hw_primary: Unable to get Power service
08-27 08:30:07.214   903  4741 D audio_hw_primary: select_devices for use case (low-latency-playback)
08-27 08:30:07.215   903  4741 D audio_hw_primary: select_devices: changing use case low-latency-playback output device from(0: , acdb -1) to (2: speaker, acdb 15)
08-27 08:30:07.215   903  4741 I msm8974_platform: platform_check_and_set_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
08-27 08:30:07.215   903  4741 I msm8974_platform: platform_check_and_set_codec_backend_cfg: new_snd_devices[0] is 2
08-27 08:30:07.215   903  4741 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: bitwidth 16, samplerate 48000 channels 2, backend_idx 0 usecase = 1 device (speaker)
01-02 03:47:20.740   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.218  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.218  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.750   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.225  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.226  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.226  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.228  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.228   903  4741 D msm8974_platform: platform_check_codec_backend_cfg:becf: updated afe: bitwidth 16, samplerate 48000 channels 2,backend_idx 0 usecase = 1 device (speaker)
08-27 08:30:07.228   903  4741 I msm8974_platform: platform_check_codec_backend_cfg:becf: afe: Codec selected backend: 0 updated bit width: 16 and sample rate: 48000
08-27 08:30:07.228   903  4741 D audio_hw_primary: check_usecases_codec_backend:becf: force routing 0
08-27 08:30:07.228   903  4741 D audio_hw_primary: check_usecases_codec_backend:becf: (93) check_usecases curr device: speaker, usecase device: backends match 0
08-27 08:30:07.228   903  4741 D audio_hw_primary: check_usecases_codec_backend:becf: check_usecases num.of Usecases to switch 0
08-27 08:30:07.228   903  4741 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 08:30:07.229   903  4741 D audio_hw_primary: enable_snd_device: snd_device(2: speaker)
08-27 08:30:07.229   903  4741 D msm8974_platform: platform_get_island_cfg_on_device:island cfg status on snd_device = (speaker 0)
08-27 08:30:07.229   903  4741 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 1, with Raise=0
08-27 08:30:07.229  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.230  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.759   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.231   903  4741 D audio_route: Apply path: speaker
08-27 08:30:07.231   903  4741 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 3, with Raise=0
08-27 08:30:07.231   903  4741 D audio_hw_utils: audio_extn_utils_send_app_type_cfg: usecase->out_snd_device speaker
08-27 08:30:07.234   903  4741 I audio_hw_utils: send_app_type_cfg_for_device PLAYBACK app_type 69937, acdb_dev_id 15, sample_rate 48000, snd_device_be_idx 39
08-27 08:30:07.235  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.235  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.238   903  4741 D ACDB-LOADER: ACDB -> send_audio_cal, acdb_id = 15, path = 0, app id = 69937, sample rate = 48000, use_case = 0,buffer_idx_w_path =0, afe_sample_rate = 48000, cal_mode = 1, offset_index = 0
08-27 08:30:07.238   903  4741 D ACDB-LOADER: ACDB -> send_asm_topology
08-27 08:30:07.239   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_STREAM_TOPOLOGY_ID
01-02 03:47:20.768   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.243   903  4741 D ACDB-LOADER: ACDB -> send_adm_topology
08-27 08:30:07.243   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_COMMON_TOPOLOGY_ID
08-27 08:30:07.243   903  4741 D ACDB-LOADER: ACDB -> send_audtable
08-27 08:30:07.243   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE_SIZE
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_COMMON_TABLE
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> AUDIO_SET_AUDPROC_CAL cal_type[11] acdb_id[15] app_type[69937]
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> send_audvoltable
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE_SIZE
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_GAIN_DEP_STEP_TABLE, vol index 0
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> AUDIO_SET_VOL_CAL cal type = 12
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE_SIZE
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> send_audstrmtable
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AUDPROC_INSTANCE_STREAM_TABLE
08-27 08:30:07.244   903  4741 D ACDB-LOADER: ACDB -> audstrm_cal->cal_type.cal_data.cal_size = 20
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> send_afe_topology
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_TOPOLOGY_ID
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> GET_AFE_TOPOLOGY_ID for adcd_id 15, Topology Id 112fc
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> send_afe_cal
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE
08-27 08:30:07.245   903  4741 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 08:30:07.245   903  4741 D ACDB-LOADER: Error: ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE_SIZE Returned = -19
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> ACDB_CMD_GET_AFE_INSTANCE_COMMON_TABLE
08-27 08:30:07.245   903  4741 D android.hardware.audio.service: Failed to fetch the lookup information of the device 0000000F 
08-27 08:30:07.245   903  4741 D ACDB-LOADER: Error: ACDB AFE returned = -19
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> AUDIO_SET_AFE_CAL cal_type[16] acdb_id[15]
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> send_hw_delay : acdb_id = 15 path = 0
08-27 08:30:07.245   903  4741 D ACDB-LOADER: ACDB -> ACDB_AVSYNC_INFO: ACDB_CMD_GET_DEVICE_PROPERTY
08-27 08:30:07.245   903  4741 D audio_hw_primary: enable_audio_route: apply mixer and update path: low-latency-playback
08-27 08:30:07.245   903  4741 D audio_route: Apply path: low-latency-playback
08-27 08:30:07.248   903  4741 D audio_hw_primary: select_devices: done
08-27 08:30:07.248   903  4741 D msm8974_platform: platform_set_channel_map mixer_ctl_name:Playback Channel Map9
08-27 08:30:07.250   903  4741 D msm8974_platform: platform_set_channel_map: set mapping(1 2 0 0 0 0 0 0) for channel:2
01-02 03:47:20.777   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.779  4741  4741 I [Awinic][1-0034]aw882xx_startup: playback enter
01-02 03:47:20.779  4741  4741 E aw_cali_get_read_cali_re: channel:0 open /mnt/vendor/persist/factory/audio/aw_cali.bin failed!
08-27 08:30:07.255  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.258  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.260  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.786   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.263  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.790  4741  4741 I [Awinic][1-0034]aw882xx_dev_init_cali_re: read nvram cali failed, use default Re
08-27 08:30:07.263  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.263  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.795   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.269  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.269  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.804   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.285  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.285  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.286  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.288  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.813   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.290  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.293  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.293  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.294  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.822   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.302  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:47:20.828  4741  4741 E msm_adsp_init_mixer_ctl_adm_pp_event_queue: failed to get kctl.
08-27 08:30:07.302  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.831   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.321  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.322  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.322  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.323  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.323  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.838  4741  4741 I afe_get_cal_topology_id: port_id = 0x1006 acdb_id = 15 topology_id = 0x112fc cal_type_index=8 ret=0
01-02 03:47:20.839  4741  4741 E send_afe_cal_type: No cal sent for cal_index 0, port_id = 0x1006! ret -22
01-02 03:47:20.840   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.324  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.850   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.324  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.324  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.850  4741  4741 I afe_send_hw_delay: port_id 0x1006 rate 48000 delay_usec 474 status 0
01-02 03:47:20.853   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.855  4741  4741 I [Awinic][1-0034]aw882xx_mute: mute state=0
01-02 03:47:20.855  4741  4741 I [Awinic][1-0034]aw882xx_spin_set_record_val: do nothing
01-02 03:47:20.855  4741  4741 I [Awinic][1-0034]aw882xx_spin_set_record_val: set record spin val done
01-02 03:47:20.855   330   330 I [Awinic][1-0034]aw882xx_startup_work: enter
01-02 03:47:20.855   330   330 I [Awinic][1-0034]aw882xx_start_pa: enter
01-02 03:47:20.855   330   330 I [Awinic][1-0034]aw882xx_dev_reg_update: done
01-02 03:47:20.856   330   330 I [Awinic][1-0034]aw_dev_pwd: done
01-02 03:47:20.858  4741  4741 E q6asm_find_cal_by_buf_number: Can't find ASM Cal for cal_index 2 app_type 69937 buffer_number 8
01-02 03:47:20.859   330   330 I [Awinic][1-0034]aw_dev_mode1_pll_check: done
01-02 03:47:20.860   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.869   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.345  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.345  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.875   330   330 I [Awinic][1-0034]aw_dev_amppd: done
08-27 08:30:07.351   903  4741 D audio_hw_primary: start_output_stream: exit
01-02 03:47:20.878   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.351  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.878   330   330 I [Awinic][1-0034]aw_dev_sysst_check: done
08-27 08:30:07.353  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.353  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.354  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.354  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.355  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.883   330   330 I [Awinic][1-0034]aw_pid_2113_reg_force_set: needn't set reg value
01-02 03:47:20.883   330   330 I [Awinic][1-0034]aw_dev_uls_hmute: done
01-02 03:47:20.887   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.896   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.896   330   330 I [Awinic][1-0034]aw_dev_mute: done
01-02 03:47:20.897   330   330 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
08-27 08:30:07.378  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:47:20.897   330   330 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
08-27 08:30:07.379  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:20.897   330   330 I [Awinic][1-0034]aw882xx_monitor_start: enter
01-02 03:47:20.897   330   330 I [Awinic][1-0034]aw882xx_device_start: done
01-02 03:47:20.897   330   330 I [Awinic][1-0034]aw882xx_start_pa: start success
01-02 03:47:20.898   330   330 I [Awinic][1-0034]aw_monitor_get_voltage: chip voltage is 3957
01-02 03:47:20.899   330   330 I [Awinic][1-0034]aw_monitor_get_temperature: reg val is 0x001c
01-02 03:47:20.899   330   330 I [Awinic][1-0034]aw_monitor_get_temperature: chip temperature = 28
01-02 03:47:20.899   330   330 I [Awinic][1-0034]aw_monitor_set_ipeak: set reg val = 0xa96c, ipeak = 0xa000
01-02 03:47:20.900   330   330 I [Awinic][1-0034]aw_monitor_set_gain: set reg val = 0x15, gain = 0x4
01-02 03:47:20.900   330   330 E [Awinic]aw_check_dsp_ready: rx topo id is 0x0
01-02 03:47:20.906   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.382  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.383  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.384  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.384  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.384  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.386  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.914   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.923   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.932   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.412  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.412  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.412  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.414  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.414  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.415  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.415  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.416  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:20.941   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.950   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.959   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:20.968   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.442  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.444  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.445  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.446  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.446  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.446  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.447  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.447  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.448   902   929 E ssgtzd  : Modemcomm: Failed to get handle of QMSCA service
01-02 03:47:20.977   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.452   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 418f went down
08-27 08:30:07.458   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4190 went down
01-02 03:47:20.986   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.463   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4191 went down
01-02 03:47:20.995   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.473  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.475  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.475  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.476  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.004   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.477  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.477  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.480  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.480  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.488  2197  2197 D b/63783866: KeyButtonView.abortCurrentGesture
01-02 03:47:21.013   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.490  2197  2197 D b/63783866: KeyButtonView.abortCurrentGesture
08-27 08:30:07.495  1046  1046 D vendor.qti.vibrator: Vibrator on for timeoutMs: 30
01-02 03:47:21.022   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.498  1046  4743 D vendor.qti.vibrator: Starting on on another thread
08-27 08:30:07.499  1252  2004 I WindowManager: Ignoring HOME; event canceled.
08-27 08:30:07.502  1252  2004 I WindowManager: Ignoring HOME; event canceled.
08-27 08:30:07.503  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.505  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.025     7     7 E spmi-0  : pmic_arb_wait_for_done: transaction failed (0x3)
01-02 03:47:21.033   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.506  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.507  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.507  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.509  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.035     7     7 E qpnp_vib_ldo_enable: Program Vibrator LDO enable is failed, ret=-5
01-02 03:47:21.044   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.044     7     7 E qpnp_vibrator_play_on: vibration enable failed, ret=-5
01-02 03:47:21.053   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.054   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.529  1046  4743 D vendor.qti.vibrator: Notifying on complete
08-27 08:30:07.530  1046  1046 D vendor.qti.vibrator: QTI Vibrator off
08-27 08:30:07.531  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.531  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.059   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.535  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.535  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.536  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.539  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.540  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.540  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.068   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.548  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.549  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.078   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.087   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.565  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.566  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.567  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.096   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.569  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.570  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.570  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.105   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.582  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.582  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.114   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.596  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.596  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.123   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.597  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.599  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.600  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.600  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.602  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.602  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.132   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.141   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.618   734   750 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
01-02 03:47:21.150   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.627  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.627  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.628  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.630  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.632  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.632  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:47:21.159   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.632  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.634  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.638   734   748 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:30:07.639   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:07.639   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
01-02 03:47:21.168   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.649  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.649  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.177   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.657  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.657  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.658  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.660  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.186   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.662  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.664  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.666  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.666  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.195   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.204   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.684  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.684  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.687  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.688  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.213   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.689  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.690  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.693  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.695  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.222   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.700  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.701  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.231   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.240   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.718  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.718  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.719  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.721  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.723  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.249   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.726  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.258   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.735  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.735  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.267   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.748  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.748  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.750  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.751  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.276   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.754  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.756  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.285   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.294   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.768  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.768  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.303   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.779  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.779  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.780  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.782  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.784  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.785  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.785  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.786  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.313   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.322   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.801  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.802  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.331   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.809  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.809  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.811  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.812  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.340   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.814  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.817  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.818  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.818  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.349   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.358   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.835  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.835  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.840  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.840  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.367   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.841  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.842  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.844  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.848  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.376   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.852  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.852  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.385   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.394   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.869  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.869  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.870  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.870  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.871  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.873  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.875  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.403   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.878  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.412   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.886  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.887  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.421   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.900  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.901  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.901  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.903  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.430   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.904  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.904  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.905  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.908  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.439   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.448   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.926  1124  1153 W GpuWork : Failed to attach bpf program to power/gpu_work_period tracepoint [2(No such file or directory)]
08-27 08:30:07.931  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.931  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.932  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.457   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.934  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.936  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.937  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.938  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:07.939  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.466   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.475   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.484   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.961  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.961  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.962  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.964  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.966  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.493   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.969  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.971  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:07.971  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.502   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.511   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.991  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.991  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.992  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.994  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:07.996  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.520   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:07.999  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.529   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.004  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.004  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.538   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.547   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.021  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.022  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.022  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.023  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.025  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.025  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.027  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.556   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.030  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.038  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.038  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.566   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.575   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.052  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.053  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.055  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.055  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.055  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.056  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.584   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.057  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.060  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.593   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.072  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.072  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.602   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.082  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.084  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.611   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.085  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.086  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.088  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.089  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.089  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.091  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.620   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.629   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.106  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.106  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.638   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.113  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.114  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.116  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.116  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.118  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.121  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.647   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.123  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.123  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.656   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.665   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.143  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.145  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.146  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.146  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.148  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.674   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.151  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.683   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.157  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.157  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.692   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.174  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.175  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.175  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.176  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.177  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.177  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.701   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.180  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.182  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.710   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.190  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.191  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.719   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.728   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.206  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.207  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.207  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.207  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.207  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.208  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.211  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.212  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.737   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.746   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.224  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.224  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.236  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.237  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.763   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.238  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.238  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.241  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.241  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.242  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.243  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.772   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.781   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.258  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.258  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.790   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.266  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.267  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.268  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.269  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.273  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.273  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.799   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.275  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.276  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.808   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.817   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.297  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.298  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.298  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.299  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.826   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.303  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.304  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.835   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.309  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.310  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.844   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.326  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.327  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.328  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.328  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.329  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.330  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.853   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.334  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.334  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.862   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.343  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
01-02 03:47:21.871   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.343  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.880   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.358  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.358  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.359  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.360  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.364  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.365  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.889   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.898   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.377  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.378  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.907   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.388  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.388  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.916   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.390  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.390  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.394  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.394  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.395  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.395  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.925   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.934   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.411  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.411  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:21.943   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.419  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.419  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.420  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.420  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.425  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.425  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.952   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.961   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.971   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.444  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.445  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.449  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.450  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.450  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.451  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:21.980   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.455  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.456  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.461  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.461  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.463   902   929 E ssgtzd  : Modemcomm: Attempt 0 : Init Instance failed with -3
01-02 03:47:21.989   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:21.998   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.478  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.478  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.479  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.480  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.480  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.007   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.481  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.486  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.486  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.016   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.495  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.495  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.025   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.034   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.510  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.511  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.511  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.511  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.512  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.512  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.516  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.516  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.043   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.052   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.529  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.529  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.061   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.541  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.541  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.541  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.542  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.070   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.546  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.546  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.546  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.547  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.079   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.088   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.563   734   749 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:30:08.564   734   751 I mpu_uart: [TIME-:25]:delete
08-27 08:30:08.564   734   749 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x35, 0x35, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x2a, ]
08-27 08:30:08.564   734   749 V mpu_uart: mcu_info:s_log_print_cnt=20355,current_state=0, ota_state = 1
08-27 08:30:08.564   734   749 V mpu_uart:  >> log: 
08-27 08:30:08.564  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.564  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.097   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.571  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.571  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.572  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.572  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.576   903  4741 W audio_hw_primary: out_write: underrun(1) frames_by_time(48120) > out->last_fifo_frames_remaining(384)
08-27 08:30:08.577  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.577  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.578   734   749 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:30:08.579   734   749 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x32, 0x39, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x32, 0x39, 0x20, 0x6d, 0x76, 0xa, 0x7f, ]
08-27 08:30:08.579   734   749 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=290 r/s,vbat=12329 mv
08-27 08:30:08.579   734   749 V mpu_uart:  >> log: 
01-02 03:47:22.106   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.584   734   748 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:30:08.584   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
01-02 03:47:22.115   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.591   734   749 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:30:08.591   734  3710 I mpu_uart: [TIME-:26]:delete
08-27 08:30:08.591   734   749 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:30:08.591   734   749 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:30:08.591   734   749 V mpu_uart:  >> log: 
08-27 08:30:08.592  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:30:08.593  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:30:08.594   734  3710 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:08.595   734  3710 I mpu_uart: [TIME-:27]:create
01-02 03:47:22.124   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.598  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.598  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.602  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.602   734   749 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:08.603   734   749 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:30:08.603  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.603   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:08.603  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.603  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.604  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:30:08.607  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:30:08.607  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.608  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.609  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
01-02 03:47:22.133   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.612   734   748 E mpu_uart: send_buff: 3c0700111491af
08-27 08:30:08.612   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:30:08.612   734   749 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:30:08.612   734   749 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x29, 0xb2, ]
08-27 08:30:08.612   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:08.616  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x29 
08-27 08:30:08.617  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
01-02 03:47:22.142   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.617  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:30:08.617  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12329
08-27 08:30:08.618  2544  2727 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12329
08-27 08:30:08.623   734   749 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:30:08.623   734  3710 I mpu_uart: [TIME-:27]:delete
08-27 08:30:08.623   734   749 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x22, 0x93, ]
08-27 08:30:08.623   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:08.624  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x22 
08-27 08:30:08.624  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:30:08.625  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
01-02 03:47:22.151   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.625  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 290
08-27 08:30:08.626  2544  2727 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=290
08-27 08:30:08.626  2544  2727 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[50, 57, 48]
08-27 08:30:08.627  2544  2727 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:30:08.633  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.633  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.633  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.634  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.160   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.638  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.638  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.169   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.649  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.649  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.178   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.187   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.663  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.663  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.664  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.665  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.668  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.668  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.196   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.205   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.214   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.694  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.694  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.694  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.695  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.223   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.698  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.699  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.699  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.700  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.232   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.241   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.724  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.724  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.724  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.250   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.725  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.729  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.729  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.259   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.268   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.751  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.751  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.277   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.755  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.755  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.755  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.756  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.759  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.759  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.286   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.296   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.305   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.785  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.785  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.785  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.786  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.314   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.790  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.790  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.323   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.801  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.801  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.332   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.815  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.816  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.816  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.816  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.341   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.819  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.819  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.820  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.821  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.350   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.359   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.368   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.846  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.846  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.846  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.847  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.850  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.851  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.852  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.852  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.377   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.386   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.395   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.869  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.869  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.876  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.876  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.877  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.404   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.877  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.880  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.881  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.413   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.422   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.902  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.902  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.431   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.906  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.906  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.907  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.908  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.911  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.912  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.440   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.919  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.919  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.449   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.458   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.936  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.936  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.937  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.937  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.937  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.938  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.467   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.941  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.942  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.476   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.954  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.954  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.485   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.967  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.967  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.967  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.495   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.969  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.970  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.971  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:08.972  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.973  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.504   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.513   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.988  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:08.988  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.522   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:08.998  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.998  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.998  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:08.999  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.002  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.003  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.531   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.540   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.022  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.022  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.549   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.028  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.029  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.029  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.030  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.558   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.033  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.034  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.567   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.576   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.056  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.056  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.059  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.059  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.059  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.060  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.585   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.064  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.065  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.594   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.603   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.612   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.089  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.089  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.090  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.090  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.090  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.090  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.095  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.095  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.621   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.630   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.110  2544  2730 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:30:09.111  2544  2730 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
01-02 03:47:22.639   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.114  3463  3779 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:30:09.115  2544  2730 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:30:09.115  2544  2730 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:30:09.116  2544  2730 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:30:09.116  2544  2730 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:30:09.117   734  3710 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:30:09.117   734  3710 I mpu_uart: [TIME-:28]:create
08-27 08:30:09.117  3463  3890 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:30:09.117  3463  3890 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:30:09.117  3463  3890 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@2d623e0
08-27 08:30:09.117  3463  3890 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:30:09.117  3463  3890 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:30:09.118  1252  2205 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3463:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:30:09.118  1252  2205 E ActivityManager: java.lang.Throwable
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:30:09.118  1252  2205 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:30:09.119  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.648   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.120  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.121  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.121  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.123  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.123  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.125  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.125  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.129   734   748 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:30:09.129   734   748 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
01-02 03:47:22.657   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.132   734   749 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:09.133   734   749 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:30:09.133   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:30:09.133   734  3710 I mpu_uart: [TIME-:28]:delete
08-27 08:30:09.135  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:30:09.136  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:30:09.136  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:30:09.137  2544  2730 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:30:09.138   734  3710 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:09.140   734  3710 I mpu_uart: [TIME-:29]:create
01-02 03:47:22.666   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.675   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.150  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.151  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.151  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.151  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.153   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:09.153   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:09.156  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.156  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.156  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.156  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.684   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.693   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.174  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.174  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.702   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.180  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.181  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.181  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.181  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.711   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.186  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.186  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.191  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.191  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.720   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.729   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.210  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.211  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.212  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.212  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.738   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.216  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.216  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.747   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.225  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.225  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.756   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.765   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.241  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.241  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.242  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.242  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.242  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.242  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.247  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.247  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.272  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.272  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.802   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.276  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.276  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.277  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.277  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.810   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.820   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.292  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.293  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.302  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.302  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.303  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.303  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.829   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.307  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.308  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.309  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.309  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.838   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.847   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.326  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.326  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.856   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.332  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.333  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.334  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.335  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.338  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.338  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.865   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.343  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.343  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.874   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.883   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.359  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.360  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.363  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.364  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.892   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.365  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.365  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.368  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.368  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.901   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.377  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.377  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.910   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.393  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.919   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.394  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.394  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.394  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.396  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.396  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.399  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.399  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.928   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.409  1252  1969 D OomAdjuster: Not killing cached processes
08-27 08:30:09.411  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.411  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.937   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:22.946   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.424  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.425  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.426  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.426  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.955   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.429  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.429  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.964   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.445  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.445  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.973   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.454  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.455  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.456  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.457  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:22.982   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.459  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.460  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.462  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.462  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:22.991   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.467   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4192 went down
08-27 08:30:09.473   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4193 went down
01-02 03:47:23.000   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.480   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4194 went down
01-02 03:47:23.009   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.484  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.486  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.487  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.487  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.490  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.490  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.018   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.495  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.496  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.027   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.512  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.512  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.036   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.515  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.516  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.517  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.517  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.520  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.520  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.045   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.529  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.529  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.054   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.063   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.546  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.546  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.546  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.547  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.547  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.548  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.072   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.551  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.550  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.081   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.563  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.563  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.090   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.100   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.577  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.578  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.578  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.579  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.109   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.580  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.580  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.581  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.583  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.118   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.597  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.598  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.127   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.607  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.608  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.608  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.136   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.609  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.612  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.613  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.614  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.615  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.615   903  4741 W audio_hw_primary: out_write: underrun(2) frames_by_time(48074) > out->last_fifo_frames_remaining(384)
01-02 03:47:23.145   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.624  2544  3893 I McuOtaServiceApp_MCUDeviceService: startReportTimer: start query voltage and fan speed
08-27 08:30:09.624  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendUartQueryVoltage
08-27 08:30:09.620  1591  1591 I tlog    : type=1400 audit(0.0:162): avc: denied { rename } for name="-00001_persist_00020_250827_083003.log.ing" dev="dm-6" ino=15143 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:30:09.624  1591  1591 I tlog    : type=1400 audit(0.0:163): avc: denied { call } for scontext=u:r:system_tlogd:s0 tcontext=u:r:hwservicemanager:s0 tclass=binder permissive=1
08-27 08:30:09.627  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x88 
08-27 08:30:09.628   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:09.629  1591  1610 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:30:09.629  1591  1610 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:09.629  1591  1610 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
08-27 08:30:09.624   579   579 W hwservicemanage: type=1400 audit(0.0:164): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
08-27 08:30:09.630   734   751 I mpu_uart: [TIME-:30]:create
01-02 03:47:23.156   579   579 I binder  : 579:579 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:47:23.156   579   579 I binder  : send failed reply for transaction 61893 to 1591:1610
08-27 08:30:09.628   579   579 W hwservicemanage: type=1400 audit(0.0:165): avc: denied { transfer } for scontext=u:r:hwservicemanager:s0 tcontext=u:r:system_tlogd:s0 tclass=binder permissive=0
01-02 03:47:23.159   579   579 I binder  : 579:579 transaction failed 29201/-1, size 28-8 line 3410
01-02 03:47:23.159   579   579 I binder  : send failed reply for transaction 61899 to 1591:1610
08-27 08:30:09.631  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.632  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.632  1591  1610 E HidlServiceManagement: getService: defaultServiceManager()->get returns Status(EX_TRANSACTION_FAILED): 'FAILED_TRANSACTION: ' for android.hidl.allocator@1.0::IAllocator/ashmem.
08-27 08:30:09.632  1591  1610 E tsnv.hidl.wrapper: TsnvWrapper:tsnv_get: ashmem getservice failed!
08-27 08:30:09.632  1591  1610 E tlogd   : [LogFiles](getBootTime) get 19 error, data=0x0
01-02 03:47:23.163   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.639  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.639  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.639  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.640  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.642  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.644  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.172   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.644  1591  1591 I tlog    : type=1400 audit(0.0:166): avc: denied { setattr } for name="-00001_persist_00021_250827_083009.log.ing" dev="dm-6" ino=15145 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:system_tlogd_file:s0 tclass=file permissive=1
08-27 08:30:09.648  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.648  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.181   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.190   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.666  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.666  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.669  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.669  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.669  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.670  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.672  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.199   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.674  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.208   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.682  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.682  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.217   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.699  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.699  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.700  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.226   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.700  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.701  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:09.701  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.703  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.704  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.235   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.244   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.253   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.730  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.730  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.730  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.731  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.733  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.735  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.270   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.750  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.750  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.279   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.760  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.760  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.761  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.762  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.288   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.765  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.765  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.297   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.306   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.315   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.791  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.793  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.793  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.793  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.795  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.795  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.324   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.800  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.801  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.333   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.342   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.821  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.823  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.823  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.823  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.351   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.826  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.826  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.360   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.369   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.851  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.851  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.852  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.378   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.853  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.853  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.854  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.856  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.856  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.387   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.396   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.405   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.882  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.884  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.884  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.884  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.886  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.886  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.414   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.423   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.902  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.902  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.432   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.912  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.914  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.914  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.915  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.917  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.917  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.441   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.450   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.459   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.943  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.945  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.468   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.945  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.945  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.947  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.947  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.478   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.952  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:09.952  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.487   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.496   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:09.973  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.975  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.975  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.975  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.977  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:09.977  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.505   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.514   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.523   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.003  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.003  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.003  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.532   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.005  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.006  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.007  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.008  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.008  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.541   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.022  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.023  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.550   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.559   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.034  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.036  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.036  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.038  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.038  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.038  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.568   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.577   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.055  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.055  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.586   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.064  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.066  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.067  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.068  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.068  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.068  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.595   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.072  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.072  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.604   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.613   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.095  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.622   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.097  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.097  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.098  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.099  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.099  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.631   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.106  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.106  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.640   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.122  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.122  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.649   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.126  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.127  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.127  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.129  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.129  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.129  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.658   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.133   734   750 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:10.139  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.139  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.667   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.676   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.153   734   748 W mpu_uart: [MSG-P:W]:wait resp time out :0-Timeout set:1000
08-27 08:30:10.154   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:10.154   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:10.156  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.158  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.158  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.159  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.160  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.160  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.685   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.694   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.173  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.174  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.703   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.187  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.188  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.712   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.189  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.190  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.190  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.190  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.190  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.190  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.721   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.730   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.207  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.207  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.739   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.217  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.218  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.220  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.220  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.221  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.221  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.748   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.758   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.767   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.241  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.241  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.248  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.776   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.249  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.250  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.251  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.251  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.252  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.258  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.258  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.785   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.794   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.275  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.275  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.803   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.279  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.281  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.812   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.281  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.281  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.282  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.282  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.292  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.292  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.821   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.830   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.310  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.311  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.312  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.312  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.312  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.313  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.839   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.848   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.326  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.326  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.857   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.340  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.866   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.342  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.342  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.342  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.342  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.343  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.875   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.884   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.360  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.360  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.893   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.371  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.372  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.372  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.373  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.373  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.374  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.902   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.377  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.377  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.911   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.394  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.394  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.920   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.402  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.402  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.403  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.403  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.929   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.404  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.404  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.410  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.411  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.938   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.948   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.427  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.428  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.433  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.434  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.435  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.965   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.444  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.445  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:23.974   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:23.983   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.462  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.462  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.463  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.463  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.464  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.464  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.464  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.467  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:23.992   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:24.001   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.480   902   929 E ssgtzd  : Modemcomm: Attempt 1 : Init Instance failed with -3
01-02 03:47:24.010   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.493  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.493  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.494  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.495  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.019   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.495  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.496  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.496  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.498  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.028   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:24.038   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.513  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.513  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.046   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.524  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.524  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.525  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.525  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.525  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.528  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.056   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.531  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.531  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.065   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.547  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.547  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.074   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.554  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.555  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.555  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.555  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.556  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.083   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.559  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.091   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.564  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.564  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.100   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.581  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.581  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.585  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.109   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.585  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.586  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.586  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.588  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.589  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.118   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.598  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.598  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.127   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:24.136   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.615  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.615  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.615  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.616  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.616  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.616  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.619  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.619  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.146   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:24.155   552   552 I get_disable_lcd_state: s_disable_lcd = 0
01-02 03:47:24.164   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.646  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.646  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.647  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.648  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.173   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.649  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.649  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.649  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.650  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.654   903  2085 D audio_hw_primary: out_standby: enter: stream (0xe3244330) usecase(1: low-latency-playback)
01-02 03:47:24.182  2085  2085 I [Awinic][1-0034]aw882xx_mute: mute state=1
01-02 03:47:24.182  2085  2085 I [Awinic][1-0034]aw882xx_monitor_stop: enter
01-02 03:47:24.184  2085  2085 I [Awinic][1-0034]aw882xx_dev_clear_int_status: done
01-02 03:47:24.185  2085  2085 I [Awinic][1-0034]aw882xx_dev_set_intmask: done
01-02 03:47:24.186  2085  2085 I [Awinic][1-0034]aw_dev_uls_hmute: done
01-02 03:47:24.190   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.666  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.666  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.199   552   552 I get_disable_lcd_state: s_disable_lcd = 0
08-27 08:30:10.676  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.677  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.677  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.679  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.681  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.681  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.218  2085  2085 I [Awinic][1-0034]aw_dev_mute: done
01-02 03:47:24.221  2085  2085 I [Awinic][1-0034]aw_dev_amppd: done
01-02 03:47:24.221  2085  2085 I [Awinic][1-0034]aw_dev_pwd: done
01-02 03:47:24.221  2085  2085 I [Awinic][1-0034]aw882xx_device_stop: done
08-27 08:30:10.700  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.700  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
01-02 03:47:24.227  2085  2085 E msm_adsp_clean_mixer_ctl_adm_pp_event_queue: failed to get kctl.
08-27 08:30:10.708  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.708  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.708  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.709  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.711  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.712  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
01-02 03:47:24.239  2085  2085 I [Awinic][1-0034]aw882xx_shutdown: stream playback
08-27 08:30:10.717  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.718  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.734  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.735  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.738  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.738  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.738  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.739  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.741  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.742  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.746   903  2085 D audio_hw_primary: disable_audio_route: reset and update mixer path: low-latency-playback
08-27 08:30:10.746   903  2085 D soundtrigger: audio_extn_sound_trigger_update_stream_status: uc_info->id 1 of type 0 for Event 2, with Raise=0
08-27 08:30:10.747   903  2085 D hardware_info: hw_info_append_hw_type : device_name = speaker
08-27 08:30:10.747   903  2085 D audio_hw_primary: disable_snd_device: snd_device(2: speaker)
08-27 08:30:10.747   903  2085 I soundtrigger: audio_extn_sound_trigger_update_device_status: device 0x2 of type 0 for Event 0, with Raise=0
08-27 08:30:10.751  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.751  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.768  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.768  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.768  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.768  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.768  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.770  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.772  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.772  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.785  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.785  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.799  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.799  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.799  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.801  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.801  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.802  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.802  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.803  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.818  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.818  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.829  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.829  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.829  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.831  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.832  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.833  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.835  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.835  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.852  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.852  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.859  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.860  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.860  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.861  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.863  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.864  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.890  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.890  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.890  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.892  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.893  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.895  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.905  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.905  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.921  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.921  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.921  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.922  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.924  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.925  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.941  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.942  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.952  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.952  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.952  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.953  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.954  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.956  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.959  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.959  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.978  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.979  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:10.982  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.982  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.983  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.984  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.985  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.986  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:10.996  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:10.996  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:11.013  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.013  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.013  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.014  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.016  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.016  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.044  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.044  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.044  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.045  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.046  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.047  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.074  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.074  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.075  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.075  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.077  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.077  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.105  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.105  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.105  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.106  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.107  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.108  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.109  1028  1113 I SDM     : StcManager::ProcessModeRenderIntent():637 Cannot find mode (gamut 255 gamma 255 intent 65535) in mode list
08-27 08:30:11.118  1028  1113 W SDM     : DisplayBase::Commit: Failed to set the data on driver for display: 54-0, Error: -1, status: 2
08-27 08:30:11.134   734   750 I mpu_uart: [MSG-P:RECV]:No message received in 1000 ms
08-27 08:30:11.135  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.136  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.135  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.136  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.137  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.138  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.154   734   748 W mpu_uart: [MSG-P:W]:wait resp time out :1-Timeout set:1000
08-27 08:30:11.154   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:11.154   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:11.166  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.166  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.166  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.167  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.168  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.169  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.196  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.196  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.197  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.197  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.198  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.199  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.227  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.227  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.227  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.228  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.229  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.230  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.258  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.258  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.258  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.259  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.259  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.260  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.288  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.288  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.288  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.290  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.290  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.291  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.319  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.319  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.319  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.320  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.320  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.321  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.349  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.349  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.349  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.350  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.350  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.351  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.379  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.379  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.379  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.381  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.381  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.382  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.410  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.410  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.411  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.411  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.411  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.412  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.441  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.441  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.441  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.442  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.442  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.443  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.471  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.471  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.471  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.472  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.473  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.473  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.483   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4195 went down
08-27 08:30:11.489   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4196 went down
08-27 08:30:11.502  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.502  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.502  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.503  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.503   902   951 E minksocket: MinkIPC_QRTR_Service: client with node 1 port 4197 went down
08-27 08:30:11.504  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.504  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.532  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.532  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.532  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.533  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.534  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.534  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.563  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.563  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.563  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.564  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.565  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.565  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.593  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.593  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.594  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.594  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.595  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.596  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.624  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.624  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.625  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.625  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.626  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.627  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.651  2493  3963 D MonitorTool_MonitorAppService: [08-27 08:30:11.645] [pool-3-thread-1:36] Starting to check status of 3 apps
08-27 08:30:11.655  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.655  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.655  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.655  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.656  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.657  2493  3963 V MonitorTool_MonitorAppService: [08-27 08:30:11.656] [pool-3-thread-1:36] App[drvsys] not detected running
08-27 08:30:11.657  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.657  2493  3963 V MonitorTool_MonitorAppService: [08-27 08:30:11.657] [pool-3-thread-1:36] App[drvsys] running status: Not running
08-27 08:30:11.658  2493  3963 W MonitorTool_MonitorAppService: [08-27 08:30:11.657] [pool-3-thread-1:36] App[drvsys] not running, attempting to start
08-27 08:30:11.663  2493  3963 E MonitorTool_MonitorAppService: [08-27 08:30:11.662] [pool-3-thread-1:36] Could not get launch intent for app[drvsys], it might not be a launchable app
08-27 08:30:11.664  2493  3963 V MonitorTool_MonitorAppService: [08-27 08:30:11.664] [pool-3-thread-1:36] App[com.thundercomm.testapp] not detected running
08-27 08:30:11.665  2493  3963 V MonitorTool_MonitorAppService: [08-27 08:30:11.665] [pool-3-thread-1:36] App[com.thundercomm.testapp] running status: Not running
08-27 08:30:11.665  2493  3963 W MonitorTool_MonitorAppService: [08-27 08:30:11.665] [pool-3-thread-1:36] App[com.thundercomm.testapp] not running, attempting to start
08-27 08:30:11.667  2493  3963 E MonitorTool_MonitorAppService: [08-27 08:30:11.666] [pool-3-thread-1:36] Could not get launch intent for app[com.thundercomm.testapp], it might not be a launchable app
08-27 08:30:11.668  2493  3963 V MonitorTool_MonitorAppService: [08-27 08:30:11.668] [pool-3-thread-1:36] App[com.ssol.titanApp] not detected running
08-27 08:30:11.668  2493  3963 V MonitorTool_MonitorAppService: [08-27 08:30:11.668] [pool-3-thread-1:36] App[com.ssol.titanApp] running status: Not running
08-27 08:30:11.669  2493  3963 W MonitorTool_MonitorAppService: [08-27 08:30:11.669] [pool-3-thread-1:36] App[com.ssol.titanApp] not running, attempting to start
08-27 08:30:11.670  2493  3963 E MonitorTool_MonitorAppService: [08-27 08:30:11.670] [pool-3-thread-1:36] Could not get launch intent for app[com.ssol.titanApp], it might not be a launchable app
08-27 08:30:11.685  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.685  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.685  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.686  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.687  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.687  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.715  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.715  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.716  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.716  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.717  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.718  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.746  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.746  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.746  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.747  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.748  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.748  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.777  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.777  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.777  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.777  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.778  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.779  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.807  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.807  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.808  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.808  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.809  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.810  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.838  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.838  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.838  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.839  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.839  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.841  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.857  1252  1377 I ActivityManager: Waited long enough for: ServiceRecord{f83a8be u0 com.qualcomm.qti.modemtestmode/.MbnSystemService}
08-27 08:30:11.869  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.869  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.869  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.869  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.870  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.871  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.899  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.899  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.899  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.900  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.900  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.903  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.930  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.930  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.930  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.930  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.931  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.933  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.960  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.960  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.960  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.961  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.961  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.964  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.991  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.991  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.991  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.991  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.991  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:11.994  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.021  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.021  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.021  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.022  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.022  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.024  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.052  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.052  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.052  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.052  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.053  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.055  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.062   734   749 D mpu_uart: [MSG-P:R-M]:recved H:71, L:0
08-27 08:30:12.062   734  3710 I mpu_uart: [TIME-:29]:delete
08-27 08:30:12.063   734   749 V mpu_uart: recv data buf:[0x3c, 0x47, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3e, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x73, 0x5f, 0x6c, 0x6f, 0x67, 0x5f, 0x70, 0x72, 0x69, 0x6e, 0x74, 0x5f, 0x63, 0x6e, 0x74, 0x3d, 0x32, 0x30, 0x33, 0x35, 0x36, 0x2c, 0x63, 0x75, 0x72, 0x72, 0x65, 0x6e, 0x74, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x3d, 0x30, 0x2c, 0x20, 0x6f, 0x74, 0x61, 0x5f, 0x73, 0x74, 0x61, 0x74, 0x65, 0x20, 0x3d, 0x20, 0x31, 0xa, 0x29, ]
08-27 08:30:12.063   734   749 V mpu_uart: mcu_info:s_log_print_cnt=20356,current_state=0, ota_state = 1
08-27 08:30:12.063   734   749 V mpu_uart:  >> log: 
08-27 08:30:12.078   734   749 D mpu_uart: [MSG-P:R-M]:recved H:68, L:0
08-27 08:30:12.078   734   749 V mpu_uart: recv data buf:[0x3c, 0x44, 0x0, 0x2, 0xd, 0x2, 0x6, 0x3b, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x77, 0x68, 0x65, 0x65, 0x6c, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x66, 0x61, 0x6e, 0x5f, 0x73, 0x70, 0x65, 0x65, 0x64, 0x3d, 0x33, 0x30, 0x30, 0x20, 0x72, 0x2f, 0x73, 0x2c, 0x76, 0x62, 0x61, 0x74, 0x3d, 0x31, 0x32, 0x33, 0x35, 0x38, 0x20, 0x6d, 0x76, 0xa, 0x71, ]
08-27 08:30:12.078   734   749 V mpu_uart: mcu_info:wheel_speed=0 r/s,fan_speed=300 r/s,vbat=12358 mv
08-27 08:30:12.078   734   749 V mpu_uart:  >> log: 
08-27 08:30:12.082  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.082  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.083   734   748 E mpu_uart: send_buff: 3c0700111488b6
08-27 08:30:12.083  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.083   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x88, 0xb6, ], send_res=7
08-27 08:30:12.082  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.083  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.086  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.091   734   749 D mpu_uart: [MSG-P:R-M]:recved H:51, L:0
08-27 08:30:12.092   734   751 I mpu_uart: [TIME-:30]:delete
08-27 08:30:12.092   734   749 V mpu_uart: recv data buf:[0x3c, 0x33, 0x0, 0x2, 0xd, 0x2, 0x6, 0x2a, 0x6d, 0x63, 0x75, 0x5f, 0x69, 0x6e, 0x66, 0x6f, 0x3a, 0x6d, 0x63, 0x75, 0x5f, 0x76, 0x65, 0x72, 0x73, 0x69, 0x6f, 0x6e, 0x3d, 0x76, 0x30, 0x2e, 0x30, 0x30, 0x2e, 0x34, 0x37, 0x2c, 0x61, 0x70, 0x70, 0x5f, 0x66, 0x6c, 0x61, 0x67, 0x3d, 0x42, 0x42, 0xa, 0x31, ]
08-27 08:30:12.092   734   749 V mpu_uart: mcu_info:mcu_version=v0.00.47,app_flag=BB
08-27 08:30:12.092   734   749 V mpu_uart:  >> log: 
08-27 08:30:12.092  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendUartQueryFanSpeed
08-27 08:30:12.093  2544  3893 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x91 
08-27 08:30:12.094   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:12.095   734   751 I mpu_uart: [TIME-:31]:create
08-27 08:30:12.101   734   749 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:12.102   734   749 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x14, 0x87, 0x0, 0xa5, ]
08-27 08:30:12.102   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:12.103  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x87 0x00 
08-27 08:30:12.103  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x87 
08-27 08:30:12.103  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU ACC ON/OFF msg !
08-27 08:30:12.108  1591  1609 E tlogd   : [StorageChecker](hasStorageSpace) space: (used: 246M, free: 33981M), path: /data/tlog/persist/
08-27 08:30:12.104  1591  1591 I tlog    : type=1400 audit(0.0:167): avc: denied { write } for name="property_service" dev="tmpfs" ino=32332 scontext=u:r:system_tlogd:s0 tcontext=u:object_r:property_socket:s0 tclass=sock_file permissive=1
08-27 08:30:12.104  1591  1591 I tlog    : type=1400 audit(0.0:168): avc: denied { connectto } for path="/dev/socket/property_service" scontext=u:r:system_tlogd:s0 tcontext=u:r:init:s0 tclass=unix_stream_socket permissive=1
08-27 08:30:12.112   734   748 E mpu_uart: send_buff: 3c0700111491af
08-27 08:30:12.112   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x91, 0xaf, ], send_res=7
08-27 08:30:12.112   734   749 D mpu_uart: [MSG-P:R-M]:recved H:9, L:0
08-27 08:30:12.113   734   751 I mpu_uart: [TIME-:31]:delete
08-27 08:30:12.113   734   749 V mpu_uart: recv data buf:[0x3c, 0x9, 0x0, 0x2, 0x14, 0x88, 0x30, 0x46, 0xdd, ]
08-27 08:30:12.113  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.113   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:12.113  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.113  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.114  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.114  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.115  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x88 0x30 0x46 
08-27 08:30:12.115  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x88 
08-27 08:30:12.115  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage msg finished !
08-27 08:30:12.115  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query voltage = 12358
08-27 08:30:12.116  2544  2727 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=voltage, data=12358
08-27 08:30:12.116  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.123   734   749 D mpu_uart: [MSG-P:R-M]:recved H:11, L:0
08-27 08:30:12.123   734   749 V mpu_uart: recv data buf:[0x3c, 0xb, 0x0, 0x2, 0x14, 0x91, 0x0, 0x0, 0x1, 0x2c, 0x9d, ]
08-27 08:30:12.123   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:14
08-27 08:30:12.124  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage type: 2 data: 0x91 0x00 0x00 0x01 0x2C 
08-27 08:30:12.125  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: data[0] = 0x91 
08-27 08:30:12.125  2544  2727 I McuOtaServiceApp_MCUDeviceService: handleUartMessage: MCU query fan speed msg finished !
08-27 08:30:12.125  2544  2727 D McuOtaServiceApp_MCUDeviceService: handleUartMessage: UART_MSG_QUERY_FAN_SPEED fanSpeed = 300
08-27 08:30:12.126  2544  2727 D McuOtaServiceApp_UartServiceBinder: onCmdCallback: cmd=fanSpeed, data=300
08-27 08:30:12.126  2544  2727 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=7, typeId=2, name=fan_speed_report, payloadData=[51, 48, 48]
08-27 08:30:12.128  2544  2727 D McuOtaServiceApp_MCUDeviceService: pushFanSpeedEvent: result = 0
08-27 08:30:12.144  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.144  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.144  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.144  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.145  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.146  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.174  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.175  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.175  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.175  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.175  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.177  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.205  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.205  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.205  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.205  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.205  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.207  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.235  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.235  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.235  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.235  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.236  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.238  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.266  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.266  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.266  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.266  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.266  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.268  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.297  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.297  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.297  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.297  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.297  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.299  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.327  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.327  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.327  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.327  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.328  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.329  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.358  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.358  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.358  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.358  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.358  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.360  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.388  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.388  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.388  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.388  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.389  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.390  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.419  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.419  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.419  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.419  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.420  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.420  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.450  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.450  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.450  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.450  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.450  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.451  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.480  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.480  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.480  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.480  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.481  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.482  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.503   902   929 E ssgtzd  : Modemcomm: Attempt 2 : Init Instance failed with -3
08-27 08:30:12.511  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.511  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.511  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.511  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.511  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.512  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.541  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.542  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.542  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.542  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.543  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.543  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.572  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.572  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.572  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.572  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.573  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.573  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.603  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.603  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.603  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.603  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.604  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.604  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.604  2544  2730 I McuOtaServiceApp_MCUDeviceService: actionAccOff: data0 = -121
08-27 08:30:12.605  2544  2730 D McuOtaServiceApp_EventBrokerManager: pushEvent: categoryId=2, typeId=1, name=ACC_OFF, payloadData=[0]
08-27 08:30:12.607  3463  3779 D EventProvider: [EventProviderApp] onEventReceived: Event received - ACC_OFF
08-27 08:30:12.607  3463  3770 D EventProvider: [EventProviderApp] TransitionEventListener
08-27 08:30:12.607  3463  3770 I EventProvider: [EventProviderApp] TransitionEventListener: onEventReceived ACC
08-27 08:30:12.607  3463  3770 I EventProvider: [EventProviderApp] handleAccStateEvent: eventEntry=vendor.thundercomm.eventbroker.EventEntry@35bb699
08-27 08:30:12.608  3463  3770 I EventProvider: [EventProviderApp] handleAccStateEvent: accState=0
08-27 08:30:12.608  3463  3770 I EventProvider: [EventProviderApp] responseAccState: accOn=false
08-27 08:30:12.608  2544  2730 I McuOtaServiceApp_MCUDeviceService: actionAccOff: result = 0
08-27 08:30:12.608  2544  2730 D McuOtaServiceApp_UartServiceBinder: onAccOff
08-27 08:30:12.608  2544  2730 D McuOtaServiceApp_MCUReportService: closeCarSpeedReport: MCU report car speed stop !
08-27 08:30:12.609  2544  2730 I McuOtaServiceApp_MCUReportService: sendDeviceMessageToUart 2: 0x80 0x00 
08-27 08:30:12.610   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:3
08-27 08:30:12.610   734   751 I mpu_uart: [TIME-:32]:create
08-27 08:30:12.610  1252  2205 E ActivityManager: Sending non-protected broadcast yellowstone.system.ACC_STATE_CHANGE from system 3463:com.thundercomm.eventprovider/u0a66 pkg com.thundercomm.eventprovider
08-27 08:30:12.610  1252  2205 E ActivityManager: java.lang.Throwable
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.checkBroadcastFromSystem(ActivityManagerService.java:13754)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:14476)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentLocked(ActivityManagerService.java:13772)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.broadcastIntentWithFeature(ActivityManagerService.java:14650)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at android.app.IActivityManager$Stub.onTransact(IActivityManager.java:2296)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at com.android.server.am.ActivityManagerService.onTransact(ActivityManagerService.java:2651)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at android.os.Binder.execTransactInternal(Binder.java:1280)
08-27 08:30:12.610  1252  2205 E ActivityManager: 	at android.os.Binder.execTransact(Binder.java:1244)
08-27 08:30:12.620   734   748 E mpu_uart: send_buff: 3c080011158000b0
08-27 08:30:12.620   734   748 V mpu_uart: send data buf:[0x3c, 0x8, 0x0, 0x11, 0x15, 0x80, 0x0, 0xb0, ], send_res=8
08-27 08:30:12.623   734   749 D mpu_uart: [MSG-P:R-M]:recved H:8, L:0
08-27 08:30:12.624   734   751 I mpu_uart: [TIME-:32]:delete
08-27 08:30:12.624   734   749 V mpu_uart: recv data buf:[0x3c, 0x8, 0x0, 0x2, 0x15, 0x80, 0x0, 0xa3, ]
08-27 08:30:12.624   734   750 D mpu_uart: [SESSION-Distribute]:Message en queue:15
08-27 08:30:12.624  2544  2730 I McuOtaServiceApp_MCUDeviceService: sendDeviceMessageToUart 1: 0x87 
08-27 08:30:12.625   734   751 D mpu_uart: [SERVICE]:sendMessage: sendMessage type:17, hdl:2
08-27 08:30:12.626   734   751 I mpu_uart: [TIME-:33]:create
08-27 08:30:12.626  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage type: 2 data: 0x80 0x00 
08-27 08:30:12.626  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage: data[0] = -128
08-27 08:30:12.626  2544  2727 D McuOtaServiceApp_MCUReportService: handleUartMessage: MCU report car speed switch finished !
08-27 08:30:12.633  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.633  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.633  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.633  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.634  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.634  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.644   734   748 E mpu_uart: send_buff: 3c0700111487b9
08-27 08:30:12.644   734   748 V mpu_uart: send data buf:[0x3c, 0x7, 0x0, 0x11, 0x14, 0x87, 0xb9, ], send_res=7
08-27 08:30:12.664  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.664  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.664  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.664  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.665  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.665  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.694  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.694  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.695  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.695  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.695  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.695  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.725  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.725  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.725  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.725  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.726  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.726  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.756  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.756  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.756  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.756  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.756  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.757  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.786  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.786  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.786  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.787  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.787  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.787  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.817  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.817  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.817  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.817  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.817  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.818  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.847  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.847  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.847  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.848  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.848  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.848  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.878  1131  1160 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.878  1131  1162 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.878  1131  1161 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.878  1131  1164 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.879  1131  1159 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
08-27 08:30:12.879  1131  1163 D spcomlib: [spcom_is_sp_subsystem_link_up] skp dev-node doesn't exist.
